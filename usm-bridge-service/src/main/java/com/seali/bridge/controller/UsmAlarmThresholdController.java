package com.seali.bridge.controller;

import com.seali.bridge.pojo.UsmAlarmThreshold;
import com.seali.bridge.dto.UsmAlarmThresholdDto;
import com.seali.bridge.form.UsmAlarmThresholdForm;
import com.seali.bridge.service.UsmAlarmThresholdService;
import com.seali.common.controller.BaseController;
import com.seali.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmAlarmThreshold")
@Api(tags = "报警阈值配置表接口")
public class UsmAlarmThresholdController extends BaseController<UsmAlarmThresholdService, UsmAlarmThreshold, UsmAlarmThresholdForm, UsmAlarmThresholdDto, String> {

    private final UsmAlarmThresholdService usmAlarmThresholdService;

    @Autowired
    public UsmAlarmThresholdController(UsmAlarmThresholdService service) {
        super(service);
        this.usmAlarmThresholdService = service;
    }

    @ApiOperation("查询设备是否到达报警阈值--test 定时任务")
    @PostMapping("/queryDeviceAlarmThreshold")
    public Result<Boolean> queryDeviceAlarmThreshold() {
        usmAlarmThresholdService.queryDeviceAlarmThreshold();
        return Result.success(true);
    }
}