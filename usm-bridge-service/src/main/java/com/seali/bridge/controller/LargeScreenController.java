package com.seali.bridge.controller;

import com.seali.bridge.entity.response.AlarmStatisticsResponse;
import com.seali.bridge.service.LargeScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 大屏接口
 */
@Api(tags = "大屏接口")
@RestController
@RequestMapping("/api/v1/largeScreen")
@RequiredArgsConstructor
@Slf4j
public class LargeScreenController {

    private final LargeScreenService largeScreenService;

    /**
     * 获取监测报警统计信息
     * 包括报警总数、已处理报警数、处理率等
     */
    @ApiOperation("4.监测报警统计")
    @PostMapping("/statistics")
    public AlarmStatisticsResponse getAlarmStatistics(@Parameter(description = "天数", example = "7") @RequestParam(defaultValue = "7") Integer dayIndex,
                                                      @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
                                                      @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        return largeScreenService.getAlarmStatistics(dayIndex, pageNum, pageSize);
    }

}
