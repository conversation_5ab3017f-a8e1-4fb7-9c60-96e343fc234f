package com.seali.bridge.controller;

import com.seali.common.controller.BaseController;
import com.seali.bridge.dto.UsmRiskDangerDto;
import com.seali.bridge.form.UsmRiskDangerForm;
import com.seali.bridge.pojo.UsmRiskDanger;
import com.seali.bridge.service.UsmRiskDangerService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmRiskDanger")
@Api(tags = "危险源信息表接口")
public class UsmRiskDangerController extends BaseController<UsmRiskDangerService, UsmRiskDanger, UsmRiskDangerForm, UsmRiskDangerDto, String> {

    private UsmRiskDangerService usmRiskDangerService;

    @Autowired
    public UsmRiskDangerController(UsmRiskDangerService service) {
        super(service);
        this.usmRiskDangerService = service;
    }
}