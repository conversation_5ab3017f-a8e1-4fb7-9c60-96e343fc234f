package com.seali.bridge.controller;

import com.hclight.common.result.PageResult;
import com.seali.bridge.entity.request.AlarmConfirmRequest;
import com.seali.bridge.entity.request.AlarmHandleRequest;
import com.seali.bridge.entity.response.AlarmHandleListResponse;
import com.seali.bridge.entity.response.AlarmTypeResponse;
import com.seali.bridge.dto.UsmMonitorAlarmDto;
import com.seali.bridge.form.UsmMonitorAlarmForm;
import com.seali.bridge.service.UsmMonitorAlarmService;
import com.seali.bridge.service.UsmMonitorAlarmStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/usmMonitorAlarm")
@Api(tags = "设备报警表接口")
public class UsmMonitorAlarmController {

    @Resource
    private UsmMonitorAlarmService usmMonitorAlarmService;

    @Resource
    private UsmMonitorAlarmStatusService usmMonitorAlarmStatusService;

    /***
     * UsmMonitorAlarm分页条件搜索实现
     * @param form
     * @param page
     * @param size
     * @return
     */
    @ApiOperation(value = "报警信息表条件分页查询",notes = "分页条件查询报警信息表方法详情" )
    @PostMapping(value = "/search/{page}/{size}" )
    public PageResult<UsmMonitorAlarmDto> findPage(@RequestBody(required = false) @ApiParam(name = "报警信息表对象",value = "传入JSON数据",required = false) UsmMonitorAlarmForm form,
                                                   @PathVariable int page, @PathVariable  int size){
        return usmMonitorAlarmService.findPage(form, page, size);
    }

    @ApiOperation(value = "报警表根据ID查询",notes = "根据ID查询报警表方法详情" )
    @ApiImplicitParam(paramType = "path", name = "id", value = "主键ID", required = true,  dataTypeClass = String.class)
    @GetMapping("/{id}")
    public UsmMonitorAlarmDto findById(@PathVariable String id){
        return usmMonitorAlarmService.findById(id);
    }

    @ApiOperation(value = "根据设备id查询报警记录",notes = "根据设备id查询报警记录方法详情" )
    @ApiImplicitParam(paramType = "path", name = "deviceId", value = "设备ID", required = true,  dataTypeClass = String.class)
    @GetMapping("/alarmRecord/{deviceId}")
    public List<UsmMonitorAlarmDto> findAlarmRecordByDeviceId(@PathVariable String deviceId){
        return usmMonitorAlarmService.findAlarmRecordByDeviceId(deviceId);
    }

    @ApiOperation(value = "报警确认",notes = "报警确认" )
    @PostMapping(value = "/alarm/confirm" )
    public void alarmConfirm(@RequestBody(required = false) AlarmConfirmRequest form) {
        usmMonitorAlarmService.alarmConfirm(form);
    }

    @ApiOperation(value = "处置新增/编辑",notes = "处置新增/编辑" )
    @PostMapping(value = "/alarm/handle" )
    public void alarmHandle(@RequestBody(required = false) AlarmHandleRequest form) {
        usmMonitorAlarmService.alarmHandle(form);
    }

    @ApiOperation(value = "处置删除",notes = "处置删除" )
    @DeleteMapping(value = "/alarm/handle/{id}" )
    public void alarmHandleDelete(@PathVariable String id) {
        usmMonitorAlarmStatusService.removeById(id);
    }

    @ApiOperation(value = "报警处置列表",notes = "报警处置列表" )
    @ApiImplicitParam(paramType = "path", name = "id", value = "主键ID", required = true,  dataTypeClass = Integer.class)
    @GetMapping("/alarm/handleList/{id}")
    public AlarmHandleListResponse alarmHandleList(@PathVariable String id) {
        return usmMonitorAlarmService.alarmHandleList(id);
    }

    @ApiOperation(value = "报警类型查询",notes = "报警类型查询" )
    @GetMapping("/getAlarmType")
    public List<AlarmTypeResponse> getAlarmType(){
        return usmMonitorAlarmService.getAlarmType();
    }
}