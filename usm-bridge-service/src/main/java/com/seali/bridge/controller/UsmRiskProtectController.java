package com.seali.bridge.controller;

import com.seali.common.controller.BaseController;
import com.seali.bridge.dto.UsmRiskProtectDto;
import com.seali.bridge.form.UsmRiskProtectForm;
import com.seali.bridge.pojo.UsmRiskProtect;
import com.seali.bridge.service.UsmRiskProtectService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmRiskProtect")
@Api(tags = "防护目标信息表接口")
public class UsmRiskProtectController extends BaseController<UsmRiskProtectService, UsmRiskProtect, UsmRiskProtectForm, UsmRiskProtectDto, String> {

    private UsmRiskProtectService usmRiskProtectService;

    @Autowired
    public UsmRiskProtectController(UsmRiskProtectService service) {
        super(service);
        this.usmRiskProtectService = service;
    }
}