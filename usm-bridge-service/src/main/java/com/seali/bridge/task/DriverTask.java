package com.seali.bridge.task;

import com.seali.bridge.service.UsmAlarmThresholdService;
import com.seali.bridge.service.UsmMonitorRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 示例定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DriverTask {

    private final UsmAlarmThresholdService usmAlarmThresholdService;

    private final UsmMonitorRecordService usmMonitorRecordService;

    /**
     * 设备监测值超阈值监测，报警
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void execute() {
        try {
            long start = System.currentTimeMillis();
            usmAlarmThresholdService.queryDeviceAlarmThreshold();
            log.info("定时任务execute执行时长：{}s", (System.currentTimeMillis() - start)/1000);
        } catch (Exception e) {
            log.error("定时任务execute执行失败", e);
        }
    }

//    @Scheduled(cron = "0 1/5 0 * * ?") TODO
    public void execute2() {
        try {
            long start = System.currentTimeMillis();
            usmMonitorRecordService.refreshDeviceOnlineStatus();
            log.info("定时任务execute2执行时长：{}s", (System.currentTimeMillis() - start)/1000);
        } catch (Exception e) {
            log.error("定时任务execute2执行失败", e);
        }
    }
}