package com.seali.bridge.service.impl;

import com.seali.bridge.pojo.UsmMaintenanceEnterprise;
import com.seali.bridge.dto.UsmMaintenanceEnterpriseDto;
import com.seali.bridge.form.UsmMaintenanceEnterpriseForm;
import com.seali.bridge.dao.UsmMaintenanceEnterpriseMapper;
import com.seali.bridge.service.UsmMaintenanceEnterpriseService;
import com.seali.common.result.Result;
import com.seali.common.service.impl.BaseServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;


@Service
@AllArgsConstructor
public class UsmMaintenanceEnterpriseServiceImpl extends BaseServiceImpl<UsmMaintenanceEnterpriseMapper, UsmMaintenanceEnterprise, UsmMaintenanceEnterpriseForm, UsmMaintenanceEnterpriseDto, String> implements UsmMaintenanceEnterpriseService {

    private final UsmMaintenanceEnterpriseMapper usmMaintenanceEnterpriseMapper;

    @Override
    protected UsmMaintenanceEnterprise formToEntity(UsmMaintenanceEnterpriseForm form) {
        UsmMaintenanceEnterprise entity = new UsmMaintenanceEnterprise();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmMaintenanceEnterpriseDto entityToDto(UsmMaintenanceEnterprise entity) {
        UsmMaintenanceEnterpriseDto dto = new UsmMaintenanceEnterpriseDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public Result<UsmMaintenanceEnterpriseDto> saveEntity(UsmMaintenanceEnterpriseForm form) {
        UsmMaintenanceEnterpriseDto enterpriseByUnifiedSocialCreditCode = getEnterpriseByUnifiedSocialCreditCode(form.getUnifiedSocialCreditCode());
        if (enterpriseByUnifiedSocialCreditCode != null){
            throw new IllegalArgumentException("统一社会信用代码已存在");
        }
        Result<UsmMaintenanceEnterpriseDto> result = super.saveEntity(form);
        return result;
    }

    @Override
    public Result<Boolean> updateEntityById(UsmMaintenanceEnterpriseForm form) {
        Result<UsmMaintenanceEnterpriseDto> entityById = getEntityById(form.getId());
        if (!entityById.getData().getUnifiedSocialCreditCode().equals(form.getUnifiedSocialCreditCode())){
            UsmMaintenanceEnterpriseDto enterpriseByUnifiedSocialCreditCode = getEnterpriseByUnifiedSocialCreditCode(form.getUnifiedSocialCreditCode());
            if (enterpriseByUnifiedSocialCreditCode != null){
                throw new IllegalArgumentException("统一社会信用代码已存在");
            }
        }

        Result<Boolean> result = super.updateEntityById(form);
        return result;
    }

    private UsmMaintenanceEnterpriseDto getEnterpriseByUnifiedSocialCreditCode(String unifiedSocialCreditCode){
        return usmMaintenanceEnterpriseMapper.getEnterpriseByUnifiedSocialCreditCode(unifiedSocialCreditCode);
    }
}