package com.seali.bridge.service.impl;

import com.hclight.common.utils.StringUtils;
import com.seali.bridge.dao.UsmMonitorIndicatorsMapper;
import com.seali.bridge.dto.UsmMonitorIndicatorsDto;
import com.seali.bridge.form.UsmMonitorIndicatorsForm;
import com.seali.bridge.pojo.UsmMonitorIndicators;
import com.seali.bridge.service.UsmMonitorIndicatorsService;
import com.seali.common.result.Result;
import com.seali.common.service.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UsmMonitorIndicatorsServiceImpl extends BaseServiceImpl<UsmMonitorIndicatorsMapper, UsmMonitorIndicators, UsmMonitorIndicatorsForm, UsmMonitorIndicatorsDto, String> implements UsmMonitorIndicatorsService {

    @Override
    protected UsmMonitorIndicators formToEntity(UsmMonitorIndicatorsForm form) {
        UsmMonitorIndicators entity = new UsmMonitorIndicators();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmMonitorIndicatorsDto entityToDto(UsmMonitorIndicators entity) {
        UsmMonitorIndicatorsDto dto = new UsmMonitorIndicatorsDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public Result<List<UsmMonitorIndicatorsDto>> getEntityByForm(UsmMonitorIndicatorsForm form) {
        Result<List<UsmMonitorIndicatorsDto>> entityByForm = super.getEntityByForm(form);
        List<UsmMonitorIndicatorsDto> data = entityByForm.getData();
        if (CollectionUtils.isEmpty(data)) {
            return entityByForm;
        }
        if (StringUtils.isNotEmpty(form.getDeviceType())){
            data.forEach(p -> {
                p.setDeviceTypeName(baseMapper.getDeviceTypeName(p.getDeviceType()));
            });
        } else {
            List<UsmMonitorIndicatorsDto> distinctData = data.stream()
                    .filter(Objects::nonNull)
                    .filter(p -> p.getDeviceType() != null)
                    .filter(p -> {
                        String deviceTypeName = baseMapper.getDeviceTypeName(p.getDeviceType());
                        boolean isValid = StringUtils.isNotEmpty(deviceTypeName);
                        if (isValid) {
                            p.setDeviceTypeName(deviceTypeName);
                        }
                        return isValid;
                    })
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    UsmMonitorIndicatorsDto::getDeviceType,
                                    Function.identity(),
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            entityByForm.setData(distinctData);
        }
        return entityByForm;
    }

    @Override
    public List<UsmMonitorIndicatorsDto> getUsmMonitorIndicatorsList(String deviceId) {
        List<UsmMonitorIndicators> usmMonitorIndicatorsList = baseMapper.getUsmMonitorIndicatorsList(deviceId);
        if(usmMonitorIndicatorsList.isEmpty()){
            return Collections.emptyList();
        }
        usmMonitorIndicatorsList.forEach(e -> {
            //mon_sjzt 转换为 monSjzt
            e.setMonitorField(toCamelCase(e.getMonitorField()));
        });
        return usmMonitorIndicatorsList.stream().map(this::entityToDto).collect(Collectors.toList());
    }

    public static String toCamelCase(String input) {
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;

        for (int i = 0; i < input.length(); i++) {
            char currentChar = input.charAt(i);

            if (currentChar == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(currentChar));
                    nextUpperCase = false;
                } else {
                    result.append(Character.toLowerCase(currentChar));
                }
            }
        }

        return result.toString();
    }

}