package com.seali.bridge.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.bridge.dao.LargeScreenMapper;
import com.seali.bridge.entity.request.ConditionRequest;
import com.seali.bridge.entity.response.AlarmStatisticsResponse;
import com.seali.bridge.service.LargeScreenService;
import com.seali.common.util.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class LargeScreenServiceImpl implements LargeScreenService {

    private final LargeScreenMapper largeScreenMapper;

    @Override
    public AlarmStatisticsResponse getAlarmStatistics(Integer dayIndex, Integer pageNum, Integer pageSize) {
        log.info("获取监测报警统计信息, 天数: {}, 分页参数: pageNum-{}, pageSize-{}", dayIndex, pageNum, pageSize);
        AlarmStatisticsResponse response = new AlarmStatisticsResponse();

        // 获取报警总数和已处理报警数
        Integer totalAlarms = largeScreenMapper.getTotalAlarms();
        Integer handledAlarms = largeScreenMapper.getHandledAlarms();

        // 设置响应数据
        response.setTotalCount(totalAlarms);
        response.setHandledCount(handledAlarms);

        // 获取各等级报警数量
        List<AlarmStatisticsResponse.AlarmLevelStatistics> alarmLevelStatistics =
                largeScreenMapper.getAlarmLevelCounts(dayIndex);
        response.setStatistics(alarmLevelStatistics);

        Page<AlarmStatisticsResponse.AlarmInfo> page = new Page<>(pageNum, pageSize);

        // 获取报警趋势（最近30天）
        ConditionRequest request = new ConditionRequest();
        LocalDateTime end = TimeUtil.now();
        LocalDateTime start = TimeUtil.minusDays(end, dayIndex);
        request.setStartDate(TimeUtil.localDateTimeToDate(start));
        request.setEndDate(TimeUtil.localDateTimeToDate(end));
        IPage<AlarmStatisticsResponse.AlarmInfo> alarmList = largeScreenMapper.findPage(page, request);
        response.setAlarmList(alarmList);

        return response;
    }
}