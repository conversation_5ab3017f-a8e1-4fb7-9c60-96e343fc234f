package com.seali.bridge.service;

import com.seali.bridge.entity.request.DeviceMapScatterPointsRequest;
import com.seali.bridge.entity.response.AlarmStatisticsResponse;
import com.seali.bridge.entity.response.DeviceMapScatterPointsResponse;

import java.util.List;

public interface LargeScreenService {


    AlarmStatisticsResponse getAlarmStatistics(Integer dayIndex, Integer pageNum, Integer pageSize);
}