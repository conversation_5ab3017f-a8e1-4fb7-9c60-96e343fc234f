package com.seali.bridge.service.impl;

import com.seali.common.service.impl.BaseServiceImpl;
import com.seali.bridge.dao.UsmRiskDangerMapper;
import com.seali.bridge.dto.UsmRiskDangerDto;
import com.seali.bridge.form.UsmRiskDangerForm;
import com.seali.bridge.pojo.UsmRiskDanger;
import com.seali.bridge.service.UsmRiskDangerService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmRiskDangerServiceImpl extends BaseServiceImpl<UsmRiskDangerMapper, UsmRiskDanger, UsmRiskDangerForm, UsmRiskDangerDto, String> implements UsmRiskDangerService {

    @Override
    protected UsmRiskDanger formToEntity(UsmRiskDangerForm form) {
        UsmRiskDanger entity = new UsmRiskDanger();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmRiskDangerDto entityToDto(UsmRiskDanger entity) {
        UsmRiskDangerDto dto = new UsmRiskDangerDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}