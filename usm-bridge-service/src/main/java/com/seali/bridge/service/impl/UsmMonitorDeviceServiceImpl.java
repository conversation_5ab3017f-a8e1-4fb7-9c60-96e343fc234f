package com.seali.bridge.service.impl;

import com.seali.bridge.entity.request.DeviceMapScatterPointsRequest;
import com.seali.bridge.entity.response.DeviceMapScatterPointsResponse;
import com.seali.bridge.enums.info.MonitorDeviceEnum;
import com.seali.bridge.pojo.UsmMonitorDevice;
import com.seali.bridge.dto.UsmMonitorDeviceDto;
import com.seali.bridge.form.UsmMonitorDeviceForm;
import com.seali.bridge.dao.UsmMonitorDeviceMapper;
import com.seali.bridge.service.UsmMonitorDeviceService;
import com.seali.bridge.util.GeometryUtils;
import com.seali.common.enums.ErrorCode;
import com.seali.common.exception.BusinessException;
import com.seali.common.service.impl.BaseServiceImpl;
import org.postgis.Geometry;
import org.postgis.Point;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

@Service
public class UsmMonitorDeviceServiceImpl extends BaseServiceImpl<UsmMonitorDeviceMapper, UsmMonitorDevice, UsmMonitorDeviceForm, UsmMonitorDeviceDto, String> implements UsmMonitorDeviceService {

    @Override
    protected UsmMonitorDevice formToEntity(UsmMonitorDeviceForm form) {
        UsmMonitorDevice entity = new UsmMonitorDevice();
        BeanUtils.copyProperties(form, entity);

        try {
            if (!StringUtils.isEmpty(form.getGeomText())) {
                Point geom = new Point(form.getGeomText());
                entity.setLongitude(String.valueOf(geom.getX()));
                entity.setLatitude(String.valueOf(geom.getY()));
                entity.setGeom(geom);
            }else if (!StringUtils.isEmpty(form.getLongitude()) && !StringUtils.isEmpty(form.getLatitude())) {
                entity.setGeom(new Point(Double.parseDouble(form.getLongitude()), Double.parseDouble(form.getLatitude())));
            }
            if (!StringUtils.isEmpty(form.getGeom3Text())) {
                Point point = new Point();
                entity.setGeom3(point);
            }
        } catch (SQLException e) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "坐标信息有误，及：geomText, longitude, latitude");
        }

        return entity;
    }

    @Override
    protected UsmMonitorDeviceDto entityToDto(UsmMonitorDevice entity) {
        UsmMonitorDeviceDto dto = new UsmMonitorDeviceDto();
        BeanUtils.copyProperties(entity, dto);

        // 处理几何数据
        Geometry geom = entity.getGeom();
        if (geom != null) {
            dto.setGeomText(GeometryUtils.postgisGeometryToWkt(geom));
        } else if (entity.getLongitude() != null && entity.getLatitude() != null
                && !StringUtils.isEmpty(entity.getLongitude()) && !StringUtils.isEmpty(entity.getLatitude())) {
            // 如果没有 geom 字段但有经纬度，则构造纯粹的 WKT 格式文本
            dto.setGeomText(String.format("POINT(%s %s)", entity.getLongitude(), entity.getLatitude()));
        }

        Geometry geom3 = entity.getGeom3();
        if (geom3 != null) {
            dto.setGeom3Text(GeometryUtils.postgisGeometryToWkt(geom3));
        }
        return dto;
    }

    @Override
    public List<DeviceMapScatterPointsResponse> getDeviceMapScatterPoints(DeviceMapScatterPointsRequest request) {
        try {
            return baseMapper.getDeviceMapScatterPoints(request);
        } catch (Exception e) {
            log.error("获取设备地图散点图数据失败", e);
            return Collections.emptyList();
        }
    }
}