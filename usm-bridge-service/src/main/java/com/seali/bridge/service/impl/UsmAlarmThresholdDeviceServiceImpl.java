package com.seali.bridge.service.impl;

import com.seali.bridge.pojo.UsmAlarmThresholdDevice;
import com.seali.bridge.dto.UsmAlarmThresholdDeviceDto;
import com.seali.bridge.form.UsmAlarmThresholdDeviceForm;
import com.seali.bridge.dao.UsmAlarmThresholdDeviceMapper;
import com.seali.bridge.service.UsmAlarmThresholdDeviceService;
import com.seali.common.service.impl.BaseServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class UsmAlarmThresholdDeviceServiceImpl extends BaseServiceImpl<UsmAlarmThresholdDeviceMapper, UsmAlarmThresholdDevice, UsmAlarmThresholdDeviceForm, UsmAlarmThresholdDeviceDto, String> implements UsmAlarmThresholdDeviceService {

    private final UsmAlarmThresholdDeviceMapper usmAlarmThresholdDeviceMapper;

    @Override
    protected UsmAlarmThresholdDevice formToEntity(UsmAlarmThresholdDeviceForm form) {
        UsmAlarmThresholdDevice entity = new UsmAlarmThresholdDevice();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmAlarmThresholdDeviceDto entityToDto(UsmAlarmThresholdDevice entity) {
        UsmAlarmThresholdDeviceDto dto = new UsmAlarmThresholdDeviceDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public List<String> getDeviceIds(String thresholdId) {
        List<String> deviceIds = usmAlarmThresholdDeviceMapper.getDeviceIds(thresholdId);
        return deviceIds;
    }
}