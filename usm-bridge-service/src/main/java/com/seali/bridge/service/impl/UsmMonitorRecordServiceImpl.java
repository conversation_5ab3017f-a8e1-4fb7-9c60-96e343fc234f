package com.seali.bridge.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.bridge.entity.request.MonitorRecordRequest;
import com.seali.bridge.entity.response.DeviceOfflineRecordResponse;
import com.seali.bridge.pojo.UsmMonitorDevice;
import com.seali.bridge.pojo.UsmMonitorOnlineStatus;
import com.seali.bridge.pojo.UsmMonitorRecord;
import com.seali.bridge.dto.UsmMonitorRecordDto;
import com.seali.bridge.form.UsmMonitorRecordForm;
import com.seali.bridge.dao.UsmMonitorRecordMapper;
import com.seali.bridge.service.UsmMonitorDeviceService;
import com.seali.bridge.service.UsmMonitorOnlineStatusService;
import com.seali.bridge.service.UsmMonitorRecordService;
import com.seali.common.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Service
@RequiredArgsConstructor
public class UsmMonitorRecordServiceImpl extends BaseServiceImpl<UsmMonitorRecordMapper, UsmMonitorRecord, UsmMonitorRecordForm, UsmMonitorRecordDto, String> implements UsmMonitorRecordService {

    private final UsmMonitorRecordMapper usmMonitorRecordMapper;

    private final UsmMonitorOnlineStatusService usmMonitorOnlineStatusService;

    private final UsmMonitorDeviceService usmMonitorDeviceService;

    @Override
    protected UsmMonitorRecord formToEntity(UsmMonitorRecordForm form) {
        UsmMonitorRecord entity = new UsmMonitorRecord();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmMonitorRecordDto entityToDto(UsmMonitorRecord entity) {
        UsmMonitorRecordDto dto = new UsmMonitorRecordDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public List<UsmMonitorRecordDto> monitorCurve(MonitorRecordRequest request) {
        List<UsmMonitorRecordDto> dto = usmMonitorRecordMapper.monitorCurve(request);
        return dto;
    }

    @Override
    public Page<UsmMonitorRecordDto> monitorCurvePage(int pageNum, int pageSize, MonitorRecordRequest request) {
        log.info("监测历史记录-在线(分页), 设备ID: {}, 页码: {}, 每页大小: {}, 开始时间: {}, 结束时间: {}",
                request.getDeviceId(), pageNum, pageSize, request.getStartTime(), request.getEndTime());

        try {
            // 创建分页对象
            Page<UsmMonitorRecordDto> page = new Page<>(pageNum, pageSize);

            // 执行分页查询
            Page<UsmMonitorRecordDto> dtoPage = usmMonitorRecordMapper.monitorCurvePage(page, request);

            log.info("查询设备监测曲线成功, 总记录数: {}, 当前页记录数: {}", dtoPage.getTotal(), dtoPage.getRecords().size());
            return dtoPage;
        } catch (Exception e) {
            log.error("查询设备监测曲线失败", e);
            throw e;
        }
    }

    @Override
    public Page<DeviceOfflineRecordResponse> queryDeviceOfflineRecords(int pageNum, int pageSize, MonitorRecordRequest request) {
        log.info("查询设备离线记录(分页), 设备ID: {}, 页码: {}, 每页大小: {}, 开始时间: {}, 结束时间: {}",
                request.getDeviceId(), pageNum, pageSize, request.getStartTime(), request.getEndTime());

        try {
            // 检查设备状态
            checkDeviceStatus(request.getDeviceId());

            // 执行分页查询
            Page<DeviceOfflineRecordResponse> resultPage = usmMonitorOnlineStatusService.queryDeviceOfflineRecords(pageNum, pageSize, request);

            log.info("查询设备离线记录成功, 总记录数: {}, 当前页记录数: {}",
                    resultPage.getTotal(), resultPage.getRecords().size());
            return resultPage;
        } catch (Exception e) {
            log.error("查询设备离线记录失败", e);
            throw e;
        }
    }

    private static final Lock lock = new ReentrantLock();

    public void refreshDeviceOnlineStatus(){
        if(!lock.tryLock()){
            return;
        }
        try{
            List<UsmMonitorDevice> devices = usmMonitorDeviceService.list();
            for (UsmMonitorDevice device : devices) {
                checkDeviceStatus(device.getId());
            }
        }finally {
            lock.unlock();
        }

    }


    private void checkDeviceStatus(String deviceId) {
        List<UsmMonitorOnlineStatus> usmMonitorRecords = usmMonitorOnlineStatusService.queryDeviceRecords(deviceId);
        if (usmMonitorRecords.isEmpty()) {
            return;
        }
        UsmMonitorOnlineStatus st_9001 = usmMonitorRecords.get(0);
        UsmMonitorOnlineStatus st_9002 = usmMonitorRecords.get(0);
        st_9002.setOfflineTime(st_9002.getMonitorTime());

        List<UsmMonitorOnlineStatus> updateRecords = new ArrayList<>();
        List<String> removeIds = new ArrayList<>();
        for (int i = 1; i < usmMonitorRecords.size(); i++) {
            UsmMonitorOnlineStatus pr = usmMonitorRecords.get(i - 1);
            UsmMonitorOnlineStatus cr = usmMonitorRecords.get(i);
            if(pr.getMonitorStatus() == 9002 && cr.getMonitorStatus() == 9001){
                st_9002.setStatus(20);//结束
                st_9002.setRecoveryTime(cr.getMonitorTime());
                st_9002.setOfflineDuration(String.valueOf((st_9002.getRecoveryTime().getTime() - st_9002.getOfflineTime().getTime()) / 1000 / 60));
                updateRecords.add(st_9002);
            }else if(pr.getMonitorStatus() == 9002 && cr.getMonitorStatus() == 9002){
                removeIds.add(cr.getId());
            }else if (pr.getMonitorStatus() == 9001 && cr.getMonitorStatus() == 9002) {
                st_9002 = cr;
                st_9002.setOfflineTime(cr.getMonitorTime());
                st_9001.setStatus(20);//结束
                updateRecords.add(pr);
            }else if (pr.getMonitorStatus() == 9001 && cr.getMonitorStatus() == 9001) {
                removeIds.add(cr.getId());
            }
        }
        if(st_9002.getRecoveryTime() == null){
            updateRecords.add(st_9002);
        }
        usmMonitorOnlineStatusService.updateBatchById(updateRecords);
        usmMonitorOnlineStatusService.removeByIds(removeIds);
    }
}