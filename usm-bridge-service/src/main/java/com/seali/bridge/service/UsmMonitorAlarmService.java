package com.seali.bridge.service;

import com.hclight.common.result.PageResult;
import com.seali.bridge.entity.request.AlarmConfirmRequest;
import com.seali.bridge.entity.request.AlarmHandleRequest;
import com.seali.bridge.entity.response.AlarmHandleListResponse;
import com.seali.bridge.entity.response.AlarmTypeResponse;
import com.seali.bridge.pojo.UsmMonitorAlarm;
import com.seali.bridge.dto.UsmMonitorAlarmDto;
import com.seali.bridge.form.UsmMonitorAlarmForm;
import com.seali.common.service.BaseService;

import java.util.List;

public interface UsmMonitorAlarmService extends BaseService<UsmMonitorAlarm, UsmMonitorAlarmForm, UsmMonitorAlarmDto, String> {

    /**
     * 报警表分页查询
     * @param form
     * @param page
     * @param size
     * @return
     */
    PageResult<UsmMonitorAlarmDto> findPage(UsmMonitorAlarmForm form, int page, int size);

    /**
     * 报警表根据id查询
     * @param id
     * @return
     */
    UsmMonitorAlarmDto findById(String id);

    /**
     * 根据设备id查询报警记录
     * @param deviceId
     * @return
     */
    List<UsmMonitorAlarmDto> findAlarmRecordByDeviceId(String deviceId);

    /**
     * 报警确认
     * @param form
     */
    void alarmConfirm(AlarmConfirmRequest form);

    void alarmHandle(AlarmHandleRequest form);

    AlarmHandleListResponse alarmHandleList(String id);

    List<AlarmTypeResponse> getAlarmType();
}