package com.seali.bridge.service.impl;

import com.seali.common.service.impl.BaseServiceImpl;
import com.seali.bridge.dao.UsmRiskProtectMapper;
import com.seali.bridge.dto.UsmRiskProtectDto;
import com.seali.bridge.form.UsmRiskProtectForm;
import com.seali.bridge.pojo.UsmRiskProtect;
import com.seali.bridge.service.UsmRiskProtectService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmRiskProtectServiceImpl extends BaseServiceImpl<UsmRiskProtectMapper, UsmRiskProtect, UsmRiskProtectForm, UsmRiskProtectDto, String> implements UsmRiskProtectService {

    @Override
    protected UsmRiskProtect formToEntity(UsmRiskProtectForm form) {
        UsmRiskProtect entity = new UsmRiskProtect();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmRiskProtectDto entityToDto(UsmRiskProtect entity) {
        UsmRiskProtectDto dto = new UsmRiskProtectDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}