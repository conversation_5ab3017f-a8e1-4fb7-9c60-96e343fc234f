package com.seali.bridge.service;

import com.seali.bridge.pojo.UsmAlarmThresholdDevice;
import com.seali.bridge.dto.UsmAlarmThresholdDeviceDto;
import com.seali.bridge.form.UsmAlarmThresholdDeviceForm;
import com.seali.common.service.BaseService;

import java.util.List;

public interface UsmAlarmThresholdDeviceService extends BaseService<UsmAlarmThresholdDevice, UsmAlarmThresholdDeviceForm, UsmAlarmThresholdDeviceDto, String> {

    List<String> getDeviceIds(String thresholdId);
}