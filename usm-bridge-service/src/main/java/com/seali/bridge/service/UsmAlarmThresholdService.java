package com.seali.bridge.service;

import com.seali.bridge.pojo.UsmAlarmThreshold;
import com.seali.bridge.dto.UsmAlarmThresholdDto;
import com.seali.bridge.form.UsmAlarmThresholdForm;
import com.seali.common.service.BaseService;

public interface UsmAlarmThresholdService extends BaseService<UsmAlarmThreshold, UsmAlarmThresholdForm, UsmAlarmThresholdDto, String> {

    /**
     * 查询设备是否到达报警阈值，并记录报警信息
     */
    void queryDeviceAlarmThreshold();
}