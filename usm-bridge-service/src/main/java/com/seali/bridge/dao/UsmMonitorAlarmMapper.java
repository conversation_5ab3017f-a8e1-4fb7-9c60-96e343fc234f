package com.seali.bridge.dao;

import com.seali.bridge.dto.UsmAlarmThresholdDto;
import com.seali.bridge.dto.UsmMonitorAlarmDto;
import com.seali.bridge.dto.UsmMonitorAlarmStatusDto;
import com.seali.bridge.form.UsmMonitorAlarmForm;
import com.seali.bridge.pojo.UsmMonitorAlarm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 设备报警表 Dao 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface UsmMonitorAlarmMapper extends BaseMapper<UsmMonitorAlarm> {

    /**
     * 报警信息分页查询
     * @param form
     * @return
     */
    List<UsmMonitorAlarmDto> findPage(UsmMonitorAlarmForm form);

    /**
     * 根据id查询报警信息
     * @param id
     * @return
     */
    UsmMonitorAlarmDto findById(@Param("id") String id);

    /**
     * 根据设备id查询报警记录
     * @param deviceId
     * @return
     */
    List<UsmMonitorAlarmDto> findAlarmRecordByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 查询报警处置列表
     * @param id
     * @return
     */
    List<UsmMonitorAlarmStatusDto> findAlarmHandleList(@Param("id") String id);

    UsmAlarmThresholdDto findAlarmThreshold(@Param("id") String id);

}
