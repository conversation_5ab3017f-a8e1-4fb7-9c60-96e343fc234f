package com.seali.bridge.dao;

import com.seali.bridge.dto.UsmMonitorIndicatorsDto;
import com.seali.bridge.entity.request.DeviceMapScatterPointsRequest;
import com.seali.bridge.entity.response.DeviceMapScatterPointsResponse;
import com.seali.bridge.entity.response.DeviceRealTimeMonitoringResponse;
import com.seali.bridge.pojo.UsmMonitorDevice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供热设备信息 Dao 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface UsmMonitorDeviceMapper extends BaseMapper<UsmMonitorDevice> {

    List<DeviceMapScatterPointsResponse> getDeviceMapScatterPoints(DeviceMapScatterPointsRequest request);

    DeviceRealTimeMonitoringResponse queryDeviceRealTimeMonitoringById(String indexCode);

    /**
     * 查询设备监测指标
     * @param deviceType
     * @param monitorIndex
     * @return
     */
    UsmMonitorIndicatorsDto findIndicators(@Param("deviceType") String deviceType, @Param("monitorIndex") String monitorIndex);

}
