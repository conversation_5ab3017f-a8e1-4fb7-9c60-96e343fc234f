package com.seali.bridge.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.bridge.entity.request.ConditionRequest;
import com.seali.bridge.entity.response.AlarmStatisticsResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LargeScreenMapper {

    /**
     * 获取报警总数
     *
     * @return 报警总数
     */
    Integer getTotalAlarms();

    /**
     * 获取已处理报警数量
     *
     * @return 已处理报警数量
     */
    Integer getHandledAlarms();

    /**
     * 获取报警等级统计
     *
     * @param days 日期范围
     * @return 报警等级统计列表
     */
    List<AlarmStatisticsResponse.AlarmLevelStatistics> getAlarmLevelCounts(@Param("days") Integer days);

    IPage<AlarmStatisticsResponse.AlarmInfo> findPage(@Param("page") Page<AlarmStatisticsResponse.AlarmInfo> page, @Param("request") ConditionRequest request);
}
