package com.seali.bridge.dao;

import com.seali.bridge.pojo.UsmMonitorIndicators;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 监测指标表 Dao 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface UsmMonitorIndicatorsMapper extends BaseMapper<UsmMonitorIndicators> {

    List<UsmMonitorIndicators> getUsmMonitorIndicatorsList(String deviceId);

    String getDeviceTypeName(String deviceType);

}
