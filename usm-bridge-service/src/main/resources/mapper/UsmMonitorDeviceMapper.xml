<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.bridge.dao.UsmMonitorDeviceMapper">

    <select id="getDeviceMapScatterPoints"
            resultType="com.seali.bridge.entity.response.DeviceMapScatterPointsResponse">
        select
        umd.id,
        umd.device_type,umd.online_status,st_astext(umd.geom) as geomText,
        CASE
        WHEN uma.alarm_level = 4001701 THEN '1'
        WHEN uma.alarm_level = 4001702 THEN '2'
        WHEN uma.alarm_level = 4001703 THEN '3'
        ELSE '0'
        END AS alarmStatus,uma.id as alarmId
        from usm_monitor_device umd left join usm_monitor_alarm uma
            on umd.id = uma.device_id and uma.alarm_status not in('4001802','4001805') and uma.is_deleted = false
        <where>
            <if test="deviceType != null and deviceType != ''">
                AND umd.device_type = #{deviceType}
            </if>
            <if test="alarmStatus != null and alarmStatus != ''">
                AND (
                CASE
                WHEN uma.alarm_level = 4001701 THEN '1'
                WHEN uma.alarm_level = 4001702 THEN '2'
                WHEN uma.alarm_level = 4001703 THEN '3'
                ELSE '0'
                END
                ) = #{alarmStatus}
            </if>
            <if test="polygon != null and polygon != ''">
                AND ST_Within(umd.geom, ST_GeomFromText(#{polygon}, 4326))
            </if>
        </where>
    </select>

    <!-- 根据设备id查询设备监测数据 -->
    <select id="queryDeviceRealTimeMonitoringById" resultType="com.seali.bridge.entity.response.DeviceRealTimeMonitoringResponse">
        SELECT
            A.id,
            '系统监测' AS monitor_source,
            A.index_code,
            A.device_name,
            A.device_type,
            A.device_type_name,
            A.monitor_object_id,
            A.monitor_object_name,
            B.id AS monitor_data_id,
            B.monitor_time,
            B.mon_fs,
            B.mon_fx,
            B.mon_wd,
            B.mon_sd,
            B.mon_dz,
            B.mon_cj,
            B.mon_wy,
            B.mon_xyqj,
            B.mon_yp,
            B.mon_zcf,
            B.mon_sl,
            B.mon_zcpw,
            B.mon_lf,
            B.mon_xjs,
            B.mon_yjs,
            B.mon_zjs,
            B.mon_czz,
            B.mon_cs,
            B.mon_rd,
            B.mon_yl,
            A.online_status,
            A.address,
            A.latitude,
            A.longitude
        FROM usm_monitor_device A
                 LEFT JOIN usm_monitor_record B ON A.id = B.device_id
        WHERE A.id = #{deviceId}
        ORDER BY B.monitor_time DESC
            LIMIT 1
    </select>

    <select id="findIndicators" resultType="com.seali.bridge.dto.UsmMonitorIndicatorsDto">
        select * from usm_monitor_indicators where device_type = #{deviceType} and monitor_index = #{monitorIndex};
    </select>

</mapper>
