<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.bridge.dao.LargeScreenMapper">
    <!-- 获取报警总数 -->
    <select id="getTotalAlarms" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM usm_monitor_alarm
        WHERE is_deleted = false
    </select>

    <!-- 获取已处理报警数量，已处置包括已处置和误报 -->
    <select id="getHandledAlarms" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM usm_monitor_alarm
        WHERE is_deleted = false
          AND (alarm_status = 9205 or alarm_status = 9202)
    </select>

    <!-- 获取报警等级统计 -->
    <select id="getAlarmLevelCounts" resultType="com.seali.bridge.entity.response.AlarmStatisticsResponse$AlarmLevelStatistics">
        SELECT
            a.alarm_level AS "alarmLevel",
            a.alarm_level_name AS "alarmLevelName",
            COUNT(1) AS "totalCount",
            COUNT(CASE WHEN a.alarm_status IN ('9202', '9205') THEN 1 END) AS "handledCount",
            ROUND(CAST(COUNT(CASE WHEN a.alarm_status IN ('9202', '9205') THEN 1 END) AS NUMERIC) /
                  NULLIF(COUNT(1), 0) * 100, 2) AS "handleRate"
        FROM usm_monitor_alarm a
        WHERE a.is_deleted = false
          AND a.alarm_time >= CURRENT_DATE - (INTERVAL '1 day' * #{days})
        GROUP BY a.alarm_level, a.alarm_level_name
        ORDER BY a.alarm_level ASC
    </select>

    <!-- 获取报警趋势 -->
    <select id="findPage" resultType="com.seali.bridge.entity.response.AlarmStatisticsResponse$AlarmInfo">
        SELECT
        a.id AS "alarmId",
        a.alarm_code AS "alarmCode",
        d.index_code AS "deviceCode",
        d.device_name AS "deviceName",
        d.device_type AS "deviceType",
        d.device_type_name AS "deviceTypeName",
        a.alarm_level AS "alarmLevel",
        a.alarm_level_name AS "alarmLevelName",
        a.alarm_source AS "alarmSource",
        a.alarm_time AS "alarmTime",
        a.alarm_status AS "handleStatus",
        a.alarm_status_name AS "handleStatusName",
        d.address AS address,
        d.longitude AS longitude,
        d.latitude AS latitude
        FROM usm_monitor_alarm a
        JOIN usm_monitor_device d ON a.device_id = d.id
        WHERE a.is_deleted = false
        <if test="request.alarmCode != null and request.alarmCode != ''">
            AND a.alarm_code = #{request.alarmCode}
        </if>
        <if test="request.alarmSource != null and request.alarmSource != ''">
            AND a.alarm_source = #{request.alarmSource}
        </if>
        <if test="request.alarmLevel != null and request.alarmLevel != ''">
            AND a.alarm_level = #{request.alarmLevel}
        </if>
        <if test="request.town != null and request.town != ''">
            AND d.region_code = #{request.town}
        </if>
        <if test="request.startDate != null and request.endDate != null">
            AND a.alarm_time BETWEEN #{request.startDate} AND #{request.endDate}
        </if>
        ORDER BY a.alarm_level DESC
    </select>
</mapper>
