<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.bridge.dao.UsmMonitorIndicatorsMapper">

    <select id="getUsmMonitorIndicatorsList" resultType="com.seali.bridge.pojo.UsmMonitorIndicators">
        select umi.*
        from usm_monitor_indicators umi
                 left join usm_monitor_device umd on umi.device_type = umd.device_type
        where umd.id = #{deviceId}
    </select>

    <select id="getDeviceTypeName" resultType="java.lang.String">
        select device_type_name from usm_monitor_device where device_type = #{deviceType} limit 1;
    </select>

</mapper>
