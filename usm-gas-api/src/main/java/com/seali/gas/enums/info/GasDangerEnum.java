package com.seali.gas.enums.info;

import lombok.Getter;

/**
 * 危险源相关枚举
 */
public class GasDangerEnum {
    
    /**
     * 建筑类型枚举
     */
    @Getter
    public enum BuildingType implements BaseEnum {
        WXHXPGC(12001, "危险化学品工厂"),
        FD(12002, "饭店"),
        GLZ(12003, "锅炉站"),
        FXY(12004, "放射源"),
        JQZ(12005, "加气站"),
        JYZ(12006, "加油站"),
        QT(12007, "其他");

        private final int code;
        private final String name;

        BuildingType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }
} 