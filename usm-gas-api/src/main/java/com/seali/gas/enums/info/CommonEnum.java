package com.seali.gas.enums.info;

import lombok.Getter;

public class CommonEnum {

    /**
     * 使用状态枚举
     */
    @Getter
    public enum UsageStatus implements BaseEnum {
        UNUSED(5001, "未使用"),
        IN_USE(5002, "使用中"),
        ABANDONED(5003, "报废");

        private final int code;
        private final String name;

        UsageStatus(int code, String name) {
            this.code = code;
            this.name = name;
        }

        /**
         * 根据code获取name
         *
         * @param code 编码
         * @return name
         */
        public static String getNameByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (UsageStatus status : values()) {
                if (status.getCode() == code) {
                    return status.getName();
                }
            }
            return null;
        }

        /**
         * 根据code获取枚举
         *
         * @param code 编码
         * @return 枚举
         */
        public static UsageStatus getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (UsageStatus status : values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }
    }
}
