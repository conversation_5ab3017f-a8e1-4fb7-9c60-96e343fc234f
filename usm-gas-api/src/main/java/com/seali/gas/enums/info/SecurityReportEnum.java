package com.seali.gas.enums.info;

import lombok.Getter;

/**
 * 安全运行报告相关枚举
 */
public class SecurityReportEnum {
    
    /**
     * 报告类型枚举
     */
    @Getter
    public enum ReportType implements BaseEnum {
        MONTH(14001, "月报"),
        QUARTER(14002, "季报"),
        YEAR(14003, "年报");

        private final int code;
        private final String name;

        ReportType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }
} 