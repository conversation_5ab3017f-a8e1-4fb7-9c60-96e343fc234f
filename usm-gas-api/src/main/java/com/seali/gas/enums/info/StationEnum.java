package com.seali.gas.enums.info;

import lombok.Getter;

public class StationEnum {

    /**
     * 场站类型枚举
     */
    @Getter
    public enum StationType implements BaseEnum {
        GATE(7001, "门站"),
        REGULATOR(7002, "调压站"),
        DISTRIBUTION(7003, "分输站"),
        STORAGE(7004, "储配站"),
        CHARGING(7005, "充装站");

        private final int code;
        private final String name;

        StationType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    /**
     * 储罐类型枚举
     */
    @Getter
    public enum TankType implements BaseEnum {
        HORIZONTAL(7101, "液化石油气(LPG)储罐"),
        VERTICAL(7102, "液化天然气(LNG)储罐"),
        SPHERICAL(7103, "压缩天然气(CNG)储罐");

        private final int code;
        private final String name;

        TankType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}