package com.seali.gas.enums.info;

import lombok.Getter;

/**
 * 防护目标相关枚举
 */
public class GasProtectEnum {
    
    /**
     * 建筑类型枚举
     */
    @Getter
    public enum BuildingType implements BaseEnum {
        XX(13001, "学校"),
        YY(13002, "医院"),
        CZ(13003, "车站"),
        SC(13004, "商场"),
        QT(13005, "其他");

        private final int code;
        private final String name;

        BuildingType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }
} 