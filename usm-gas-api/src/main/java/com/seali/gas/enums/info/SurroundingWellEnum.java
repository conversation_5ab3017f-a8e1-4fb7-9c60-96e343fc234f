package com.seali.gas.enums.info;

import lombok.Getter;

/**
 * 燃气周边相邻空间窨井相关枚举
 */
public class SurroundingWellEnum {
    
    /**
     * 窨井类型枚举
     */
    @Getter
    public enum WellType implements BaseEnum {
        RAIN(15001, "雨水井"),
        SEWAGE(15002, "污水井"),
        CONFLUENCE(15003, "合流窨井"),
        COMMUNICATION(15004, "通信井"),
        POWER(15005, "电力井"),
        HEAT(15006, "热力井");

        private final int code;
        private final String name;

        WellType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    /**
     * 窨井类型枚举
     */
    @Getter
    public enum WellShapeType implements BaseEnum {
        ROUND(9101, "圆形"),
        SQUARE(9102, "方形"),
        OTHER(9103, "其他");

        private final int code;
        private final String name;

        WellShapeType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    /**
     * 窨井类型枚举
     */
    @Getter
    public enum WellMaterialType implements BaseEnum {
        ZT(9001, "铸铁"),
        FHCL(9002, "复合材料"),
        G(9003, "钢"),
        BXG(9004, "不锈钢"),
        JYX(9005, "聚乙烯"),
        LHJ(9006, "铝合金"),
        HNT(9007, "混凝土"),
        QT(9008, "其他");

        private final int code;
        private final String name;

        WellMaterialType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }
} 