package com.seali.gas.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorAlarmStatus对象", description = "报警状态表")
@TableName(value = "usm_monitor_alarm_status", autoResultMap = true)
public class UsmMonitorAlarmStatus extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("报警ID")
    @TableField("alarm_id")
    private Long alarmId;

    @ApiModelProperty("报警状态（9201:待确认, 9202:误报, 9203:待处置, 9204:处置中, 9205:已处置, 9206:已归档）")
    @TableField("alarm_status")
    private Integer alarmStatus;

    @ApiModelProperty("报警状态名称")
    @TableField("alarm_status_name")
    private String alarmStatusName;

    @ApiModelProperty("描述")
    @TableField("description")
    private String description;

    @ApiModelProperty("单位")
    @TableField("unit")
    private String unit;

    @ApiModelProperty("图片地址")
    @TableField("pic_urls")
    private String picUrls;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;

    @ApiModelProperty(value = "确认结果（93001：真实报警，93002：误报）")
    @TableField("confirm_result")
    private Integer confirmResult;

    @ApiModelProperty(value = "确认结果名称")
    @TableField("confirm_result_name")
    private String confirmResultName;

    @ApiModelProperty(value = "处置状态（94001：处置中，94002：处置完成）")
    @TableField("handle_status")
    private Integer handleStatus;

    @ApiModelProperty(value = "处置状态名称")
    @TableField("handle_status_name")
    private String handleStatusName;

    @ApiModelProperty(value = "处置人")
    @TableField("handle_user")
    private String handleUser;
}