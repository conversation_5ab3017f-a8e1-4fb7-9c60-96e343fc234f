package com.seali.gas.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmFixedPointLaserMethaneMonitor对象", description = "固定点式激光甲烷监测仪")
@TableName(value = "usm_fixed_point_laser_methane_monitor", autoResultMap = true)
public class UsmFixedPointLaserMethaneMonitor extends BasePojo {

    @ApiModelProperty("数据唯一标识")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("数据唯一标识")
    @TableField("data_id")
    private String dataId;

    @ApiModelProperty("设备编码")
    @TableField("index_code")
    private String indexCode;

    @ApiModelProperty("监测数据，字段使用设备监测项编码")
    @TableField("wl")
    private String wl;

    @ApiModelProperty("监测时间")
    @TableField("monitor_time")
    private Timestamp monitorTime;

    @ApiModelProperty("入库时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("温度（单位：℃）")
    @TableField("temperature")
    private BigDecimal temperature;

    @ApiModelProperty("湿度（单位：%RH）")
    @TableField("humidity")
    private BigDecimal humidity;

    @ApiModelProperty("液位距离（单位：cm）")
    @TableField("wld")
    private Integer wld;

    @ApiModelProperty("甲烷浓度（单位：ppm）")
    @TableField("methane_concentration")
    private Integer methaneConcentration;

    @ApiModelProperty("井盖状态：0 正常；1 异常")
    @TableField("manhole_cover_status")
    private Integer manholeCoverStatus;

    @ApiModelProperty("参考信号接收功率")
    @TableField("rsrp")
    private Integer rsrp;

    @ApiModelProperty("信号强度")
    @TableField("rssi")
    private Integer rssi;

    @ApiModelProperty("信噪比")
    @TableField("snr")
    private Integer snr;

    @ApiModelProperty("电压（单位：V）")
    @TableField("voltage")
    private BigDecimal voltage;

    @ApiModelProperty("设备状态：0 正常；1 超量程；2、超工作温度；3 甲烷传感器光路污染；4、甲烷传感器光强饱和；5、液位报警；6、甲烷冷凝结露；7、甲烷传感器温控失锁")
    @TableField("work_status")
    private Integer workStatus;

}