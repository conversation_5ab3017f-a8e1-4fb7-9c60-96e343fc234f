package com.seali.gas.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmZyGasSurroundingWell对象", description = "燃气周边相邻空间窨井信息表")
@TableName(value = "usm_zy_gas_surrounding_well", autoResultMap = true)
public class UsmZyGasSurroundingWell extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("窨井编码")
    @TableField("well_code")
    private String wellCode;

    @ApiModelProperty("窨井类型（15001：雨水井，15002：污水井，15003：合流窨井，15004：通信井，15005：电力井，15006：热力井）")
    @TableField("well_type")
    private Integer wellType;

    @ApiModelProperty("窨井类型名称")
    @TableField("well_type_name")
    private String wellTypeName;

    @ApiModelProperty("井深（m）")
    @TableField("well_depth")
    private BigDecimal wellDepth;

    @ApiModelProperty("所在道路")
    @TableField("road_name")
    private String roadName;

    @ApiModelProperty("窨井形状（9101:圆形, 9102:方形, 9103:其他）")
    @TableField("well_shape")
    private Integer wellShape;

    @ApiModelProperty("井盖形状名称")
    @TableField("well_shape_name")
    private String wellShapeName;

    @ApiModelProperty("窨井材质（9001:铸铁, 9002:不锈钢, 9003:复合材料, 9004:钢, 9005:混凝土, 9006:其他）")
    @TableField("well_material")
    private Integer wellMaterial;

    @ApiModelProperty("井盖材质名称")
    @TableField("well_material_name")
    private String wellMaterialName;

    @ApiModelProperty("井盖尺寸")
    @TableField("well_size")
    private String wellSize;

    @ApiModelProperty("井室规格")
    @TableField("well_room_standard")
    private String wellRoomStandard;

    @ApiModelProperty("特征点")
    @TableField("feature")
    private String feature;

    @ApiModelProperty("附属物")
    @TableField("attached_facilities")
    private String attachedFacilities;

    @ApiModelProperty("埋深（m）")
    @TableField("buried_depth")
    private BigDecimal buriedDepth;

    @ApiModelProperty("地面高程")
    @TableField("elevation")
    private BigDecimal elevation;

    @ApiModelProperty("关联管线id")
    @TableField("pipeline_id")
    private Long pipelineId;

    @ApiModelProperty("关联管线编码")
    @TableField("pipeline_code")
    private String pipelineCode;

    @ApiModelProperty("建设时间")
    @TableField("construction_time")
    private Timestamp constructionTime;

    @ApiModelProperty("权属单位code")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("窨井几何形状")
    @TableField("geom")
    private Object geom;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;

}