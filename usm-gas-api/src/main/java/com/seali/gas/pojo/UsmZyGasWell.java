package com.seali.gas.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.seali.common.pojo.BasePojo;
import com.seali.gas.handler.PostGisGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.postgis.Geometry;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmZyGasWell对象", description = "燃气窨井信息表")
@TableName(value = "usm_zy_gas_well", autoResultMap = true)
public class UsmZyGasWell extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("窨井编码")
    @TableField("well_code")
    private String wellCode;

    @ApiModelProperty("窨井形状（9101:圆形, 9102:方形, 9103:其他）")
    @TableField("well_shape")
    private Integer wellShape;

    @ApiModelProperty("窨井形状名称")
    @TableField("well_shape_name")
    private String wellShapeName;

    @ApiModelProperty("窨井材质（9001:铸铁, 9002:不锈钢, 9003:复合材料, 9004:钢, 9005:混凝土, 9006:其他）")
    @TableField("well_material")
    private Integer wellMaterial;

    @ApiModelProperty("窨井材质名称")
    @TableField("well_material_name")
    private String wellMaterialName;

    @ApiModelProperty("窨井尺寸")
    @TableField("well_size")
    private String wellSize;

    @ApiModelProperty("井深(米)")
    @TableField("well_depth")
    private BigDecimal wellDepth;

    @ApiModelProperty("埋深(米)")
    @TableField("buried_depth")
    private BigDecimal buriedDepth;

    @ApiModelProperty("高程(米)")
    @TableField("elevation")
    private BigDecimal elevation;

    @ApiModelProperty("附属设施描述")
    @TableField("attached_facilities")
    private String attachedFacilities;

    @ApiModelProperty("所在道路名称")
    @TableField("road_name")
    private String roadName;

    @ApiModelProperty("关联管线信息")
    @TableField("connected_pipeline")
    private String connectedPipeline;

    @ApiModelProperty("权属单位编码")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("空间坐标")
    @TableField(value = "geom", typeHandler = PostGisGeometryTypeHandler.class)
    @JsonIgnore  // 忽略序列化该字段，避免循环引用
    private Geometry geom;

    @ApiModelProperty("空间坐标文本表示")
    @TableField(exist = false)
    private String geomText;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("安装时间")
    @TableField("install_time")
    private Date installTime;

    @ApiModelProperty("使用状态（5001:未使用, 5002:使用中, 5003:废弃）")
    @TableField("usage_status")
    private Integer usageStatus;

    @ApiModelProperty("使用状态名称")
    @TableField("usage_status_name")
    private String usageStatusName;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;

}