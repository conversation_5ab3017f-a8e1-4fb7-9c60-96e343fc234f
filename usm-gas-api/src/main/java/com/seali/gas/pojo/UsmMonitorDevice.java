package com.seali.gas.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.seali.common.pojo.BasePojo;
import com.seali.gas.handler.PostGisGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.postgis.Geometry;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorDevice对象", description = "监测设备信息")
@TableName(value = "usm_monitor_device", autoResultMap = true)
public class UsmMonitorDevice extends BasePojo {

    @ApiModelProperty("设备唯一标识")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("设备唯一标识")
    @TableField("data_id")
    private String dataId;

    @ApiModelProperty("设备编码")
    @TableField("index_code")
    private String indexCode;

    @ApiModelProperty("设备名称")
    @TableField("device_name")
    private String deviceName;

    @ApiModelProperty("设备类型编码")
    @TableField("device_type")
    private String deviceType;

    @ApiModelProperty("设备类型名称")
    @TableField("device_type_name")
    private String deviceTypeName;

    @ApiModelProperty("监测类型")
    @TableField("monitor_type")
    private String monitorType;

    @ApiModelProperty("监测类型名称")
    @TableField("monitor_type_name")
    private String monitorTypeName;

    @ApiModelProperty("监测指标编码")
    @TableField("monitor_index")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    @TableField("monitor_index_name")
    private String monitorIndexName;

    @ApiModelProperty("监测对象编码")
    @TableField("monitor_target")
    private String monitorTarget;

    @ApiModelProperty("监测对象名称")
    @TableField("monitor_target_name")
    private String monitorTargetName;

    @ApiModelProperty("监测对象id（管网、场站、窨井）")
    @TableField("monitor_object_id")
    private String monitorObjectId;

    @ApiModelProperty("监测对象名称")
    @TableField("monitor_object_name")
    private String monitorObjectName;

    @ApiModelProperty("采集频率(次/分钟)")
    @TableField("collect_frequency")
    private BigDecimal collectFrequency;

    @ApiModelProperty("上传频率(次/分钟)")
    @TableField("upload_frequency")
    private BigDecimal uploadFrequency;

    @ApiModelProperty("量程下线")
    @TableField("measure_range_low")
    private String measureRangeLow;

    @ApiModelProperty("量程上线")
    @TableField("measure_range_up")
    private String measureRangeUp;

    @ApiModelProperty("量程单位")
    @TableField("measure_unit")
    private String measureUnit;

    @ApiModelProperty("区域编码")
    @TableField("region_code")
    private String regionCode;

    @ApiModelProperty("区域名称")
    @TableField("region_name")
    private String regionName;

    @ApiModelProperty("区域Path")
    @TableField("region_path")
    private String regionPath;

    @ApiModelProperty("区域全称")
    @TableField("region_path_name")
    private String regionPathName;

    @ApiModelProperty("安装地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("空间坐标，及经纬度")
    @TableField(value = "geom", typeHandler = PostGisGeometryTypeHandler.class)
    @JsonIgnore  // 忽略序列化该字段，避免循环引用
    private Geometry geom;

    @ApiModelProperty("空间坐标文本表示")
    @TableField(exist = false)
    private String geomText;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private String longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private String latitude;

    @ApiModelProperty("在线状态")
    @TableField("online_status")
    private Integer onlineStatus;

    @ApiModelProperty("有视频能力")
    @TableField("is_vss")
    private String isVss;

    @ApiModelProperty("图片URL列表")
    @TableField("pic_urls")
    private String picUrls;

    @ApiModelProperty("权属单位Code")
    @TableField("ownership_unit")
    private String ownershipUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("ownership_unit_name")
    private String ownershipUnitName;

    @ApiModelProperty("数据更新时间")
    @TableField("time")
    private Timestamp time;

}