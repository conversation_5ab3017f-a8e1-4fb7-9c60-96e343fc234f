package com.seali.gas.entity.response.risk.monitor;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 处置、误报率统计响应
 */
@Data
@ApiModel("处置、误报率统计响应")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlarmDisposalSituationResponse {

    @ApiModelProperty("处置完成率")
    private String completionRate;

    @ApiModelProperty("误报率")
    private String falseAlarmRate;

    @ApiModelProperty("平均处置时长")
    private String avgHandlingDuration;

    @ApiModelProperty("处置完成率环比")
    private String completionAnalysis;

    @ApiModelProperty("处置完成率环比趋势(up：上升/down：下降)")
    private String completionAnalysisTrend;

    @ApiModelProperty("误报率环比")
    private String falseAlarmAnalysis;

    @ApiModelProperty("误报率环比趋势(up：上升/down：下降)")
    private String falseAlarmAnalysisTrend;

    @ApiModelProperty("处置时长环比")
    private String avgHandlingDurationAnalysis;

    @ApiModelProperty("处置时长环比趋势(up：上升/down：下降)")
    private String avgHandlingDurationAnalysisTrend;

    @Data
    public static class MetricTrendAnalysis {
        private Double changeRate;      // 变化率(百分比)
        private String trend;           // 趋势(up/down)
    }

}