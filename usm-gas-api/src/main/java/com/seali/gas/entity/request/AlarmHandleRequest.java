package com.seali.gas.entity.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 新增报警处置请求
 */
@Data
@ApiModel(description = "新增报警处置请求")
public class AlarmHandleRequest {

    @ApiModelProperty(value = "报警id")
    private Long alarmId;

    @ApiModelProperty(value = "处置状态（94001：处置中，94002：处置完成）")
    private Integer handleStatus;

    @ApiModelProperty(value = "处置描述")
    private String description;

    @ApiModelProperty(value = "处置照片")
    private String picUrls;

    @ApiModelProperty(value = "处置时间")
    private Date handleTime;

    @ApiModelProperty(value = "处置人员")
    private String handleUser;

    @ApiModelProperty(value = "处置人员单位")
    private String unit;

    @ApiModelProperty(value = "备注")
    private String remarks;
} 