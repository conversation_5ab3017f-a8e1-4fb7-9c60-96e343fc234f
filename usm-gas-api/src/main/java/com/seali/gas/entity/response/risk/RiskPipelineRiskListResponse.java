package com.seali.gas.entity.response.risk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 管线风险清单响应
 */
@Data
@ApiModel( "管线风险清单响应")
public class RiskPipelineRiskListResponse {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("管线编码")
    private String pipelineCode;

    @ApiModelProperty("管龄")
    private String pipelineAge;

    @ApiModelProperty("管材")
    private String pipelineMaterial;

    @ApiModelProperty("管材名称")
    private String pipelineMaterialName;

    @ApiModelProperty("管径")
    private String pipelineDiameter;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @ApiModelProperty("风险等级名称")
    private String riskLevelName;
} 