package com.seali.gas.entity.response.risk.monitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 企业报警信息统计响应
 */
@Data
@ApiModel("企业报警信息统计响应")
public class AlarmEnterPriseStatisticsResponse {

    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty("报警总数")
    private Integer alarmCount;

    @ApiModelProperty("已处置数量")
    private Integer handledCount;

    @ApiModelProperty("处置率")
    private Double handleRate;

    @ApiModelProperty("一级报警数")
    private Integer level1Count;

    @ApiModelProperty("一级报警已处置数量")
    private Integer level1HandledCount;

    @ApiModelProperty("一级报警处置率")
    private Double level1HandleRate;

    @ApiModelProperty("二级报警数")
    private Integer level2Count;

    @ApiModelProperty("二级报警已处置数量")
    private Integer level2HandledCount;

    @ApiModelProperty("二级报警处置率")
    private Double level2HandleRate;

    @ApiModelProperty("三级报警数")
    private Integer level3Count;

    @ApiModelProperty("三级报警已处置数量")
    private Integer level3HandledCount;

    @ApiModelProperty("三级报警处置率")
    private Double level3HandleRate;
} 