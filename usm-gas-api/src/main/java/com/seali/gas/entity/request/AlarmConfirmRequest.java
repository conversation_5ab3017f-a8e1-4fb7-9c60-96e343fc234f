package com.seali.gas.entity.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 报警确认请求
 */
@Data
@ApiModel(description = "报警确认请求")
public class AlarmConfirmRequest {

    @ApiModelProperty(value = "报警id")
    private Long alarmId;

    @ApiModelProperty(value = "确认结果（93001：真实报警，93002：误报）")
    private Integer confirmResult;

    @ApiModelProperty(value = "确认描述")
    private String description;

    @ApiModelProperty(value = "确认人")
    private String handleUser;
} 