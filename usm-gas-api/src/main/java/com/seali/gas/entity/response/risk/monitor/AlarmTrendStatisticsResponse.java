package com.seali.gas.entity.response.risk.monitor;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 报警趋势统计响应
 */
@Data
@ApiModel("报警趋势统计响应")
public class AlarmTrendStatisticsResponse {

    @ApiModelProperty("报警趋势统计列表")
    private List<DailyAlarmStatistics> statistics;

    @Data
    @ApiModel("每日报警统计")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DailyAlarmStatistics {
        @ApiModelProperty("统计日期")
        private LocalDate date;

        @ApiModelProperty("报警总数")
        private Integer totalCount;

        @ApiModelProperty("一级报警数量")
        private Integer level1Count;

        @ApiModelProperty("二级报警数量")
        private Integer level2Count;

        @ApiModelProperty("三级报警数量")
        private Integer level3Count;

        @ApiModelProperty("四级报警数量")
        private Integer level4Count;
    }
} 