package com.seali.gas.entity.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 设备监测记录信息查询条件
 */
@Data
@ApiModel(description = "设备监测记录信息查询条件")
public class MonitorRecordRequest {

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "1-曲线，2-历史记录，3-离线记录")
    private Integer type;

    @ApiModelProperty(value = "开始日期")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    private Date endTime;
} 