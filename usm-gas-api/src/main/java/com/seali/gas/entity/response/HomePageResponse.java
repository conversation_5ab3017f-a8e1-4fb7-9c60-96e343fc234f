package com.seali.gas.entity.response;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.seali.gas.dto.UsmFzGasExpertDto;
import com.seali.gas.dto.UsmFzGasSchemeDto;
import com.seali.gas.dto.UsmMonitorAlarmStatusDto;
import com.seali.gas.entity.response.risk.monitor.AlarmStatisticsResponse;
import com.seali.gas.entity.response.risk.monitor.AlarmTrendStatisticsResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 首页响应
 */
@Data
@ApiModel("首页响应")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HomePageResponse {

    @ApiModelProperty("今日报警数")
    private Integer todayCount;

    @ApiModelProperty("本月报警数")
    private Integer monthCount;

    @ApiModelProperty("1级报警数")
    private Integer level1count;

    @ApiModelProperty("2级报警数")
    private Integer level2count;

    @ApiModelProperty("3级报警数")
    private Integer level3count;

    @ApiModelProperty("报警列表")
    private Page<AlarmStatisticsResponse.AlarmInfo> alarmInfoPage;

    @ApiModelProperty("报警总数")
    private Integer alarmCount;

    @ApiModelProperty("已处置报警数")
    private Integer handleCount;

    @ApiModelProperty("处置完成率")
    private Integer handleRate;

    @ApiModelProperty("报警曲线数据")
    private List<AlarmTrendStatisticsResponse.DailyAlarmStatistics> alarmTrendStatistics;

}
