package com.seali.gas.entity.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 设备离线记录响应
 */
@Data
@ApiModel("设备离线记录响应")
public class DeviceOfflineRecordResponse {

    @ApiModelProperty("设备ID")
    private String deviceId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("离线时间")
    private Timestamp offlineTime;

    @ApiModelProperty("恢复时间")
    private Timestamp recoveryTime;

    @ApiModelProperty("离线时长（分钟）")
    private String offlineDuration;
}
