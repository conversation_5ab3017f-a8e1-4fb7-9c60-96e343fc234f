package com.seali.gas.entity.response.risk.monitor;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 高发报警设备响应
 */
@Data
@ApiModel("高发报警设备响应")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlarmHighFrequencyAlarmDeviceResponse {

    @ApiModelProperty("设备编码")
    private String deviceCode;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备类型")
    private String deviceType;

    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;

    @ApiModelProperty("设备位置")
    private String address;

    @ApiModelProperty("报警总数")
    private Integer alarmCount;

    @ApiModelProperty("已处置数量")
    private Integer handledCount;

    @ApiModelProperty("处置率")
    private Double handleRate;

    @ApiModelProperty("一级报警数")
    private Integer level1Count;

    @ApiModelProperty("一级报警已处置数量")
    private Integer level1HandledCount;

    @ApiModelProperty("一级报警处置率")
    private Double level1HandleRate;

    @ApiModelProperty("二级报警数")
    private Integer level2Count;

    @ApiModelProperty("二级报警已处置数量")
    private Integer level2HandledCount;

    @ApiModelProperty("二级报警处置率")
    private Double level2HandleRate;

    @ApiModelProperty("三级报警数")
    private Integer level3Count;

    @ApiModelProperty("三级报警已处置数量")
    private Integer level3HandledCount;

    @ApiModelProperty("三级报警处置率")
    private Double level3HandleRate;
} 