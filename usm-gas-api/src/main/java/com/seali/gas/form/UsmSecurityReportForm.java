package com.seali.gas.form;

import com.seali.common.form.BaseForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmSecurityReportForm", description = "表单对象")
public class UsmSecurityReportForm extends BaseForm {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("报告编码")
    private String reportCode;

    @ApiModelProperty("报告名称")
    private String reportName;

    @ApiModelProperty("报告类型（14001：月报，14002：季报，14003：年报）")
    private Integer reportType;

    @ApiModelProperty("报告类型名称")
    private String reportTypeName;

    @ApiModelProperty("文件地址")
    private String fileUrl;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

}