package com.seali.gas.form;

import com.seali.common.form.BaseForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmZyGasSurroundingWellForm", description = "燃气周边相邻空间窨井信息表表单对象")
public class UsmZyGasSurroundingWellForm extends BaseForm {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("窨井编码")
    private String wellCode;

    @ApiModelProperty("窨井类型（15001：雨水井，15002：污水井，15003：合流窨井，15004：通信井，15005：电力井，15006：热力井）")
    private Integer wellType;

    @ApiModelProperty("窨井类型名称")
    private String wellTypeName;

    @ApiModelProperty("井深（m）")
    private BigDecimal wellDepth;

    @ApiModelProperty("所在道路")
    private String roadName;

    @ApiModelProperty("窨井形状（9101:圆形, 9102:方形, 9103:其他）")
    private Integer wellShape;

    @ApiModelProperty("井盖形状名称")
    private String wellShapeName;

    @ApiModelProperty("窨井材质（9001:铸铁, 9002:不锈钢, 9003:复合材料, 9004:钢, 9005:混凝土, 9006:其他）")
    private Integer wellMaterial;

    @ApiModelProperty("井盖材质名称")
    private String wellMaterialName;

    @ApiModelProperty("井盖尺寸")
    private String wellSize;

    @ApiModelProperty("井室规格")
    private String wellRoomStandard;

    @ApiModelProperty("特征点")
    private String feature;

    @ApiModelProperty("附属物")
    private String attachedFacilities;

    @ApiModelProperty("埋深（m）")
    private BigDecimal buriedDepth;

    @ApiModelProperty("地面高程")
    private BigDecimal elevation;

    @ApiModelProperty("关联管线id")
    private Long pipelineId;

    @ApiModelProperty("关联管线编码")
    private String pipelineCode;

    @ApiModelProperty("建设时间")
    private Timestamp constructionTime;

    @ApiModelProperty("权属单位code")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    private String managementUnitName;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("区县编码")
    private String county;

    @ApiModelProperty("区县名称")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    private String townName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("窨井几何形状")
    private Object geom;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    private String remarks;

}