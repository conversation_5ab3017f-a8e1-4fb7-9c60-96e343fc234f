package com.seali.gas.form;

import com.seali.common.form.BaseForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmZyGasDangerForm", description = "危险源信息表表单对象")
public class UsmZyGasDangerForm extends BaseForm {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("危险源编码")
    private String dangerCode;

    @ApiModelProperty("危险源名称")
    private String dangerName;

    @ApiModelProperty("建筑类型编码（12001：危险化学品工厂，12002：饭店，12003：锅炉站，12004：放射源，12005：加气站，12006：加油站，12007：其他）")
    private Integer buildingType;

    @ApiModelProperty("建筑类型名称")
    private String buildingTypeName;

    @ApiModelProperty("是否重大危险源（0：否，1：是）")
    private String isMajor;

    @ApiModelProperty("特征描述")
    private String featureDesc;

    @ApiModelProperty("风险等级")
    private String riskLevelName;

    @ApiModelProperty("影响半径（KM）")
    private BigDecimal influenceRadius;

    @ApiModelProperty("财产损失")
    private String propertyLoss;

    @ApiModelProperty("所属单位编码")
    private String managementUnit;

    @ApiModelProperty("所属单位名称")
    private String managementUnitName;

    @ApiModelProperty("联系人")
    private String contactUser;

    @ApiModelProperty("联系电话")
    private String contactInfo;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("区县编码")
    private String county;

    @ApiModelProperty("区县名称")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    private String townName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("危险源名称/编码")
    private String nameOrCode;

}