package com.seali.gas.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorAlarmStatusDto", description = "报警状态表数据传输对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsmMonitorAlarmStatusDto extends BaseDto {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("报警ID")
    private Long alarmId;

    @ApiModelProperty("报警状态（9201:待确认, 9202:误报, 9203:待处置, 9204:处置中, 9205:已处置, 9206:已归档）")
    private Integer alarmStatus;

    @ApiModelProperty("报警状态名称")
    private String alarmStatusName;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("图片地址")
    private String picUrls;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty(value = "确认结果（93001：真实报警，93002：误报）")
    private Integer confirmResult;

    @ApiModelProperty(value = "确认结果名称")
    private String confirmResultName;

    @ApiModelProperty(value = "处置状态（94001：处置中，94002：处置完成）")
    private Integer handleStatus;

    @ApiModelProperty(value = "处置状态名称")
    private String handleStatusName;

    @ApiModelProperty(value = "处置人")
    private String handleUser;

}