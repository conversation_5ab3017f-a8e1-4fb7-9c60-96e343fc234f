package com.seali.gas.dto;

import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmZyGasProtectDto", description = "防护目标信息表数据传输对象")
public class UsmZyGasProtectDto extends BaseDto {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("防护目标编码")
    private String protectCode;

    @ApiModelProperty("防护目标名称")
    private String protectName;

    @ApiModelProperty("建筑类型编码（13001：学校，13002：医院，13003：车站，13004：商场，13005：其他）")
    private Integer buildingType;

    @ApiModelProperty("建筑类型名称")
    private String buildingTypeName;

    @ApiModelProperty("是否重点防护目标（0：否，1：是）")
    private String isMajor;

    @ApiModelProperty("建筑面积")
    private String buildingArea;

    @ApiModelProperty("满负荷人数")
    private Integer fullPeopleNumber;

    @ApiModelProperty("建筑年份")
    private String buildingYear;

    @ApiModelProperty("所属单位编码")
    private String managementUnit;

    @ApiModelProperty("所属单位编码名称")
    private String managementUnitName;

    @ApiModelProperty("联系人")
    private String contactUser;

    @ApiModelProperty("联系电话")
    private String contactInfo;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("区县编码")
    private String county;

    @ApiModelProperty("区县名称")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    private String townName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;

}