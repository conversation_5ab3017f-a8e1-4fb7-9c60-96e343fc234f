package com.seali.gas.dto;

import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmSecurityReportDto", description = "数据传输对象")
public class UsmSecurityReportDto extends BaseDto {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("报告编码")
    private String reportCode;

    @ApiModelProperty("报告名称")
    private String reportName;

    @ApiModelProperty("报告类型（14001：月报，14002：季报，14003：年报）")
    private Integer reportType;

    @ApiModelProperty("报告类型名称")
    private String reportTypeName;

    @ApiModelProperty("文件地址")
    private String fileUrl;

    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;

}