package com.seali.gas.dto;

import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmZyGasWellDto", description = "燃气窨井信息表数据传输对象")
public class UsmZyGasWellDto extends BaseDto {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("窨井编码")
    private String wellCode;

    @ApiModelProperty("窨井形状（9101:圆形, 9102:方形, 9103:其他）")
    private Integer wellShape;

    @ApiModelProperty("窨井形状名称")
    private String wellShapeName;

    @ApiModelProperty("窨井材质（9001:铸铁, 9002:不锈钢, 9003:复合材料, 9004:钢, 9005:混凝土, 9006:其他）")
    private Integer wellMaterial;

    @ApiModelProperty("窨井材质名称")
    private String wellMaterialName;

    @ApiModelProperty("窨井尺寸")
    private String wellSize;

    @ApiModelProperty("井深(米)")
    private BigDecimal wellDepth;

    @ApiModelProperty("埋深(米)")
    private BigDecimal buriedDepth;

    @ApiModelProperty("高程(米)")
    private BigDecimal elevation;

    @ApiModelProperty("附属设施描述")
    private String attachedFacilities;

    @ApiModelProperty("所在道路名称")
    private String roadName;

    @ApiModelProperty("关联管线信息")
    private String connectedPipeline;

    @ApiModelProperty("权属单位编码")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    private String managementUnitName;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("区县编码")
    private String county;

    @ApiModelProperty("区县名称")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    private String townName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("空间坐标文本表示")
    private String geomText;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("安装时间")
    private Date installTime;

    @ApiModelProperty("使用状态（5001:未使用, 5002:使用中, 5003:废弃）")
    private Integer usageStatus;

    @ApiModelProperty("使用状态名称")
    private String usageStatusName;

    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;

}