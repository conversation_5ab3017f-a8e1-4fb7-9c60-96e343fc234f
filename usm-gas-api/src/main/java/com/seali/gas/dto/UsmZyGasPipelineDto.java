package com.seali.gas.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmZyGasPipelineDto", description = "管线信息表数据传输对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsmZyGasPipelineDto extends BaseDto {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("管线编码")
    private String pipelineCode;

    @ApiModelProperty("压力级别（1001:低压, 1002:中压, 1003:高压）")
    private Integer pressureLevel;

    @ApiModelProperty("压力级别名称")
    private String pressureLevelName;

    @ApiModelProperty("埋设类型（2001:地埋事铺设, 2002:架空铺设, 2003:水平定向钻铺设, 2004:管道隧道铺设）")
    private Integer buriedType;

    @ApiModelProperty("埋设类型名称")
    private String buriedTypeName;

    @ApiModelProperty("设计压力(Mpa)")
    private BigDecimal designPressure;

    @ApiModelProperty("管径(DN)")
    private String pipeDiameter;

    @ApiModelProperty("长度(m)")
    private BigDecimal pipeLength;

    @ApiModelProperty("流向")
    private String flowDirection;

    @ApiModelProperty("所在道路")
    private String roadName;

    @ApiModelProperty("管线类型（3001:非空管, 3002:空管, 3003:井内连线, 3004:垂直管线段, 3005:过河的架空线段, 3006:非开挖管(顶管)线段, 3007:顶管）")
    private Integer pipeType;

    @ApiModelProperty("管线类型名称")
    private String pipeTypeName;

    @ApiModelProperty("起点埋深(m)")
    private BigDecimal startPointDepth;

    @ApiModelProperty("起点高程(m)")
    private BigDecimal startPointDistance;

    @ApiModelProperty("起点经度")
    private BigDecimal startPointLongitude;

    @ApiModelProperty("起点纬度")
    private BigDecimal startPointLatitude;

    @ApiModelProperty("终点埋深(m)")
    private BigDecimal endPointDepth;

    @ApiModelProperty("终点高程(m)")
    private BigDecimal endPointDistance;

    @ApiModelProperty("终点经度")
    private BigDecimal endPointLongitude;

    @ApiModelProperty("终点纬度")
    private BigDecimal endPointLatitude;

    @ApiModelProperty("材质（4001:钢管, 4002:PE管, 4003:PVC管, 4004:不锈钢管, 4005:铸铁管, 4006:其他）")
    private Integer material;

    @ApiModelProperty("材质名称")
    private String materialName;

    @ApiModelProperty("建设时间")
    private Timestamp constructionTime;

    @ApiModelProperty("权属单位编码")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    private String managementUnitName;

    @ApiModelProperty("所属市")
    private String city;

    @ApiModelProperty("所属区县编码")
    private String county;

    @ApiModelProperty("所属区县名称")
    private String countyName;

    @ApiModelProperty("所属乡镇编码")
    private String town;

    @ApiModelProperty("所属乡镇名称")
    private String townName;

    @ApiModelProperty("使用状态（5001:未使用, 5002:使用中, 5003:废弃）")
    private Integer usageStatus;

    @ApiModelProperty("使用状态名称")
    private String usageStatusName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("空间坐标文本表示")
    private String geomText;

    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("总长度(km)")
    private BigDecimal totalLength;

    @ApiModelProperty("本年增长长度")
    private BigDecimal increaseLength;

    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("百分比")
    private String percent;

    @ApiModelProperty("管线年龄")
    private String age;

    @ApiModelProperty("低压管网长度(km)")
    private BigDecimal lowLength;

    @ApiModelProperty("中压管网长度(km)")
    private BigDecimal mediumLength;

    @ApiModelProperty("高压管网长度(km)")
    private BigDecimal highLength;

    @ApiModelProperty("管网维修次数")
    private Integer repairCount;
}