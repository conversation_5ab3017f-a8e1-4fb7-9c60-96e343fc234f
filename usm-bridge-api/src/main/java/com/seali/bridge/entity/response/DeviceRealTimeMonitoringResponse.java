package com.seali.bridge.entity.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 设备实时监测响应
 */
@Data
@ApiModel("设备实时监测响应")
public class DeviceRealTimeMonitoringResponse {

    @ApiModelProperty("设备ID")
    private String id;

    @ApiModelProperty("监测来源")
    private String monitorSource;

    @ApiModelProperty("设备编码")
    private String indexCode;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备类型")
    private String deviceType;

    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;

    @ApiModelProperty("监测指标编码")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    private String monitorIndexName;

    @ApiModelProperty("监测对象ID")
    private String monitorObjectId;

    @ApiModelProperty("监测对象名称")
    private String monitorObjectName;

    @ApiModelProperty("监测数据ID")
    private String monitorDataId;

    @ApiModelProperty("监测值")
    private String value;

    @ApiModelProperty("监测时间")
    private Timestamp monitorTime;

    @ApiModelProperty("设备工作状态")
    private Integer workStatus;

    @ApiModelProperty("设备工作状态描述")
    private String workStatusDesc;

    @ApiModelProperty("在线状态")
    private Integer onlineStatus;

    @ApiModelProperty("在线状态描述")
    private String onlineStatusDesc;

    @ApiModelProperty("权属单位Code")
    private String ownershipUnit;

    @ApiModelProperty("权属单位名称")
    private String ownershipUnitName;

    @ApiModelProperty("安装地址")
    private String address;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("风速(m/s)")
    private Object monFs;

    @ApiModelProperty("风向(°)")
    private Object monFx;

    @ApiModelProperty("温度(℃)")
    private Object monWd;

    @ApiModelProperty("湿度(%)")
    private Object monSd;

    @ApiModelProperty("地震加速度(m/s²)")
    private Object mon_dz;

    @ApiModelProperty("车船撞击加速度(m/s²)")
    private Object mon_cj;

    @ApiModelProperty("位移/变形(mm)")
    private Object mon_wy;

    @ApiModelProperty("XY倾角(°)")
    private Object monXyqj;

    @ApiModelProperty("应变(με)")
    private Object monYp;

    @ApiModelProperty("支座反力(kN)")
    private Object monZcf;

    @ApiModelProperty("索力(kN)")
    private Object monSl;

    @ApiModelProperty("支座偏位(mm)")
    private Object monZcpw;

    @ApiModelProperty("裂缝(mm)")
    private Object monLf;

    @ApiModelProperty("X向加速度(cm/s²)")
    private Object monXjs;

    @ApiModelProperty("Y向加速度(cm/s²)")
    private Object monYjs;

    @ApiModelProperty("Z向加速度(cm/s²)")
    private Object monZjs;

    @ApiModelProperty("车重/轴重(kg)")
    private Object monCzz;

    @ApiModelProperty("车速(km/h)")
    private Object monCs;

    @ApiModelProperty("挠度（单位：mm）")
    private Object monRd;

    @ApiModelProperty("应力（单位：MPa）")
    private Object monYl;
}
