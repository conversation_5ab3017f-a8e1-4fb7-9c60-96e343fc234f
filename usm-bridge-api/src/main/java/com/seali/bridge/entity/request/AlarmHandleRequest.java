package com.seali.bridge.entity.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 新增报警处置请求
 */
@Data
@ApiModel(description = "新增报警处置请求")
public class AlarmHandleRequest {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "报警id")
    private String alarmId;

    @ApiModelProperty(value = "处置状态（4002101：处置中，4002102：处置完成）")
    private Integer handleStatus;

    @ApiModelProperty(value = "处置描述")
    private String description;

    @ApiModelProperty(value = "处置照片")
    private String picUrls;

    @ApiModelProperty(value = "处置时间")
    private Date createTime;

    @ApiModelProperty(value = "处置人员")
    private String handleUser;

    @ApiModelProperty(value = "处置人员单位")
    private String unit;

    @ApiModelProperty(value = "备注")
    private String remarks;
} 