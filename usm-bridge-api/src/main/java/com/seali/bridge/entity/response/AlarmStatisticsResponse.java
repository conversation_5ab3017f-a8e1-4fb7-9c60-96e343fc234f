package com.seali.bridge.entity.response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * 监测报警统计响应
 */
@Data
@ApiModel("监测报警统计响应")
public class AlarmStatisticsResponse {

    @ApiModelProperty("报警总数")
    private Integer totalCount;

    @ApiModelProperty("已处置数量")
    private Integer handledCount;

    @ApiModelProperty("处置完成率")
    private Double handledRate;

    @ApiModelProperty("报警列表")
    private IPage<AlarmInfo> alarmList;

    @ApiModelProperty("报警等级统计列表")
    private List<AlarmLevelStatistics> statistics;

    @Data
    @ApiModel("报警等级统计")
    public static class AlarmLevelStatistics {
        @ApiModelProperty("报警等级")
        private String alarmLevel;

        @ApiModelProperty("报警等级名称")
        private String alarmLevelName;

        @ApiModelProperty("报警总数")
        private Integer totalCount;

    }

    @Data
    @ApiModel("报警信息")
    public static class AlarmInfo {
        @ApiModelProperty("报警ID")
        private String alarmId;

        @ApiModelProperty("报警编号")
        private String alarmCode;

        @ApiModelProperty("设备编码")
        private String deviceCode;

        @ApiModelProperty("设备名称")
        private String deviceName;

        @ApiModelProperty("设备类型")
        private String deviceType;

        @ApiModelProperty("设备类型名称")
        private String deviceTypeName;

        @ApiModelProperty("报警值")
        private String alarmValue;

        @ApiModelProperty("报警等级")
        private String alarmLevel;

        @ApiModelProperty("报警等级名称")
        private String alarmLevelName;

        @ApiModelProperty("报警来源、报警类型")
        private String alarmSource;

        @ApiModelProperty("报警时间")
        private Timestamp alarmTime;

        @ApiModelProperty("处置状态")
        private String handleStatus;

        @ApiModelProperty("处置状态名称")
        private String handleStatusName;

        @ApiModelProperty("报警地址")
        private String address;

        @ApiModelProperty("经度")
        private String longitude;

        @ApiModelProperty("维度")
        private String latitude;
    }
} 