package com.seali.bridge.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 日期范围请求
 */
@Data
@ApiModel(description = "燃气报警信息查询条件")
public class ConditionRequest {

    @ApiModelProperty("报警编号")
    private String alarmCode;

    @ApiModelProperty("报警来源、报警类型")
    private String alarmSource;

    @ApiModelProperty("报警等级（9101:一级, 9102:二级, 9103:三级, 9104:四级）")
    private int alarmLevel;

    @ApiModelProperty("乡镇、街道")
    private String town;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始日期", example = "2024-01-01")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束日期", example = "2024-01-31")
    private Date endDate;
} 