package com.seali.bridge.entity.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "DeviceMapScatterPointsResponse", description = "设备地图散点图响应对象")
@Data
public class DeviceMapScatterPointsResponse {

    @ApiModelProperty(value = "设备ID")
    private String id;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "设备状态（0：离线，1：在线）")
    private String onlineStatus;

    @ApiModelProperty(value = "设备空间信息")
    private String geomText;

    @ApiModelProperty(value = "报警等级（0：正常，1：一级报警，2：二级报警，3：三级报警）")
    private String alarmStatus;

    @ApiModelProperty(value = "报警id")
    private String alarmId;
}
