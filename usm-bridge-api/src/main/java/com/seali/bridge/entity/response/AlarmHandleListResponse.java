package com.seali.bridge.entity.response;

import com.seali.bridge.dto.UsmMonitorAlarmStatusDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 报警处置列表响应
 */
@Data
@ApiModel("报警处置列表响应")
public class AlarmHandleListResponse {

    @ApiModelProperty("报警处置列表")
    private List<UsmMonitorAlarmStatusDto> usmMonitorAlarmStatusDtos;

//    @ApiModelProperty("关联处置方案列表")
//    private List<UsmFzGasSchemeDto> usmFzGasSchemeDtos;
//
//    @ApiModelProperty("关联专家列表")
//    private List<UsmFzGasExpertDto> usmFzGasExpertDtos;

}
