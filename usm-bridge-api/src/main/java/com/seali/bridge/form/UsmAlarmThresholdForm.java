package com.seali.bridge.form;

import com.seali.common.form.BaseForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmAlarmThresholdForm", description = "报警阈值配置表表单对象")
public class UsmAlarmThresholdForm extends BaseForm {

    @ApiModelProperty("")
    private String id;

    @ApiModelProperty("规则名称")
    private String ruleName;

    @ApiModelProperty("规则描述")
    private String ruleDesc;

    @ApiModelProperty("是否启用")
    private Boolean isEnabled;

    @ApiModelProperty("设备类型")
    private String deviceType;

    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;

    @ApiModelProperty("监测指标")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    private String monitorIndexName;

    @ApiModelProperty("设备选择：设备ID列表")
    private List<String> deviceIds;

    @ApiModelProperty("一级报警阈值下限")
    private BigDecimal thresholdLevel1Min;

    @ApiModelProperty("一级报警阈值上限")
    private BigDecimal thresholdLevel1Max;

    @ApiModelProperty("一级报警权属单位通知")
    private Boolean notifyRightsDept1;

    @ApiModelProperty("一级报警监管部门通知")
    private Boolean notifySuperviseDept1;

    @ApiModelProperty("一级报警通知的监管部门ID列表")
    private String notifySuperviseDeptIds1;

    @ApiModelProperty("二级报警阈值下限")
    private BigDecimal thresholdLevel2Min;

    @ApiModelProperty("二级报警阈值上限")
    private BigDecimal thresholdLevel2Max;

    @ApiModelProperty("二级报警权属单位通知")
    private Boolean notifyRightsDept2;

    @ApiModelProperty("二级报警监管部门通知")
    private Boolean notifySuperviseDept2;

    @ApiModelProperty("二级报警通知的监管部门ID列表")
    private String notifySuperviseDeptIds2;

    @ApiModelProperty("三级报警阈值下限")
    private BigDecimal thresholdLevel3Min;

    @ApiModelProperty("三级报警阈值上限")
    private BigDecimal thresholdLevel3Max;

    @ApiModelProperty("三级报警权属单位通知")
    private Boolean notifyRightsDept3;

    @ApiModelProperty("三级报警监管部门通知")
    private Boolean notifySuperviseDept3;

    @ApiModelProperty("三级报警通知的监管部门ID列表")
    private String notifySuperviseDeptIds3;

    @ApiModelProperty("系统通知")
    private Boolean notifySystem;

    @ApiModelProperty("邮件通知")
    private Boolean notifyEmail;

    @ApiModelProperty("短信通知")
    private Boolean notifySms;

    @ApiModelProperty("一级报警通知的权属单位ID")
    private String notifyRightsDeptIds1;

    @ApiModelProperty("二级报警通知的权属单位ID")
    private String notifyRightsDeptIds2;

    @ApiModelProperty("三级报警通知的权属单位ID")
    private String notifyRightsDeptIds3;

}