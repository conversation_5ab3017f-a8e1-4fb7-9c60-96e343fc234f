package com.seali.bridge.form;

import com.seali.common.form.BaseForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorDeviceForm", description = "供热设备信息表单对象")
public class UsmMonitorDeviceForm extends BaseForm {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("设备唯一标识")
    private String dataId;

    @ApiModelProperty("设备编码")
    private String indexCode;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备类型编码（2001301：管网流量，2001302：管网热量，2001303：管网温度，2001304：用户室温，2001305：管网压力）")
    private String deviceType;

    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;

    @ApiModelProperty("监测类型")
    private String monitorType;

    @ApiModelProperty("监测类型名称")
    private String monitorTypeName;

    @ApiModelProperty("监测指标编码（5004003：桥梁温度监测（℃），5004004：桥梁湿度监测（%RH），5004008：桥梁位移/变形监测（mm），5004010：桥梁应变监测（με），5004015：桥梁裂缝监测（mm），5004019：桥梁挠度监测（mm））")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    private String monitorIndexName;

    @ApiModelProperty("监测对象编码（4001501：桥梁）")
    private String monitorTarget;

    @ApiModelProperty("监测对象名称")
    private String monitorTargetName;

    @ApiModelProperty("监测对象id（桥梁）")
    private String monitorObjectId;

    @ApiModelProperty("监测对象名称")
    private String monitorObjectName;

    @ApiModelProperty("采集频率(次/分钟)")
    private BigDecimal collectFrequency;

    @ApiModelProperty("上传频率(次/分钟)")
    private BigDecimal uploadFrequency;

    @ApiModelProperty("量程下线")
    private String measureRangeLow;

    @ApiModelProperty("量程上线")
    private String measureRangeUp;

    @ApiModelProperty("量程单位")
    private String measureUnit;

    @ApiModelProperty("区域编码")
    private String regionCode;

    @ApiModelProperty("区域名称")
    private String regionName;

    @ApiModelProperty("区域Path")
    private String regionPath;

    @ApiModelProperty("区域全称")
    private String regionPathName;

    @ApiModelProperty("安装地址")
    private String address;

    @ApiModelProperty("空间坐标，及经纬度")
    private String geomText;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("在线状态（0：离线，1：在线）")
    private Integer onlineStatus;

    @ApiModelProperty("有视频能力")
    private String isVss;

    @ApiModelProperty("图片URL列表")
    private String picUrls;

    @ApiModelProperty("数据更新时间")
    private Timestamp time;

    @ApiModelProperty("权属单位Code")
    private String ownershipUnit;

    @ApiModelProperty("权属单位名称")
    private String ownershipUnitName;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("定位(三维)")
    private String geom3Text;

    @ApiModelProperty("桥梁名称")
    private String bridgeName;
}