package com.seali.bridge.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorOnlineStatus对象", description = "设备监测在线记录")
@TableName(value = "usm_monitor_online_status", autoResultMap = true)
public class UsmMonitorOnlineStatus extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("中台Id")
    @TableField("data_id")
    private String dataId;

    @ApiModelProperty("设备ID")
    @TableField("device_id")
    private String deviceId;

    @ApiModelProperty("监测时间")
    @TableField("monitor_time")
    private Timestamp monitorTime;

    @ApiModelProperty("监测状态（9001:在线, 9002:离线）")
    @TableField("monitor_status")
    private Integer monitorStatus;

    @ApiModelProperty("监测状态名称")
    @TableField("monitor_status_name")
    private String monitorStatusName;

    @ApiModelProperty("离线时间")
    @TableField("offline_time")
    private Timestamp offlineTime;

    @ApiModelProperty("恢复时间")
    @TableField("recovery_time")
    private Timestamp recoveryTime;

    @ApiModelProperty("离线时长（分钟）")
    @TableField("offline_duration")
    private String offlineDuration;

    @ApiModelProperty("离线时长（分钟）")
    @TableField("offline_duration")
    private String status;

}