package com.seali.bridge.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.seali.bridge.handler.PostGisGeometryTypeHandler;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.postgis.Geometry;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorDevice对象", description = "供热设备信息")
@TableName(value = "usm_monitor_device", autoResultMap = true)
public class UsmMonitorDevice extends BasePojo {

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("设备唯一标识")
    @TableField("data_id")
    private String dataId;

    @ApiModelProperty("设备编码")
    @TableField("index_code")
    private String indexCode;

    @ApiModelProperty("设备名称")
    @TableField("device_name")
    private String deviceName;

    @ApiModelProperty("设备类型编码（2001301：管网流量，2001302：管网热量，2001303：管网温度，2001304：用户室温，2001305：管网压力）")
    @TableField("device_type")
    private String deviceType;

    @ApiModelProperty("设备类型名称")
    @TableField("device_type_name")
    private String deviceTypeName;

    @ApiModelProperty("监测类型")
    @TableField("monitor_type")
    private String monitorType;

    @ApiModelProperty("监测类型名称")
    @TableField("monitor_type_name")
    private String monitorTypeName;

    @ApiModelProperty("监测指标编码（2001401：管网流量监测（m3/h），2001402：管网热量监测（热量：MW），2001405：管网温度监测（温度：℃），2001406：用户室温监测（温度：℃），2001407：管网压力监测（压力：Mpa））")
    @TableField("monitor_index")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    @TableField("monitor_index_name")
    private String monitorIndexName;

    @ApiModelProperty("监测对象编码（2001501：管线，2001502：热源，2001503：换热站，2001504：用户，2001505：窨井）")
    @TableField("monitor_target")
    private String monitorTarget;

    @ApiModelProperty("监测对象名称")
    @TableField("monitor_target_name")
    private String monitorTargetName;

    @ApiModelProperty("监测对象id（管网、场站、窨井）")
    @TableField("monitor_object_id")
    private String monitorObjectId;

    @ApiModelProperty("监测对象名称")
    @TableField("monitor_object_name")
    private String monitorObjectName;

    @ApiModelProperty("采集频率(次/分钟)")
    @TableField("collect_frequency")
    private BigDecimal collectFrequency;

    @ApiModelProperty("上传频率(次/分钟)")
    @TableField("upload_frequency")
    private BigDecimal uploadFrequency;

    @ApiModelProperty("量程下线")
    @TableField("measure_range_low")
    private String measureRangeLow;

    @ApiModelProperty("量程上线")
    @TableField("measure_range_up")
    private String measureRangeUp;

    @ApiModelProperty("量程单位")
    @TableField("measure_unit")
    private String measureUnit;

    @ApiModelProperty("区域编码")
    @TableField("region_code")
    private String regionCode;

    @ApiModelProperty("区域名称")
    @TableField("region_name")
    private String regionName;

    @ApiModelProperty("区域Path")
    @TableField("region_path")
    private String regionPath;

    @ApiModelProperty("区域全称")
    @TableField("region_path_name")
    private String regionPathName;

    @ApiModelProperty("安装地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("空间坐标，及经纬度")
    @TableField(value = "geom", typeHandler = PostGisGeometryTypeHandler.class)
    @JsonIgnore  // 忽略序列化该字段，避免循环引用
    private Geometry geom;

    @ApiModelProperty("空间坐标文本表示")
    @TableField(exist = false)
    private String geomText;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private String longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private String latitude;

    @ApiModelProperty("在线状态（0：离线，1：在线）")
    @TableField("online_status")
    private Integer onlineStatus;

    @ApiModelProperty("有视频能力")
    @TableField("is_vss")
    private String isVss;

    @ApiModelProperty("图片URL列表")
    @TableField("pic_urls")
    private String picUrls;

    @ApiModelProperty("数据更新时间")
    @TableField("time")
    private Timestamp time;

    @ApiModelProperty("权属单位Code")
    @TableField("ownership_unit")
    private String ownershipUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("ownership_unit_name")
    private String ownershipUnitName;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("定位(三维)")
    @TableField(value = "geom_3", typeHandler = PostGisGeometryTypeHandler.class)
    @JsonIgnore  // 忽略序列化该字段，避免循环引用
    private Geometry geom3;

    @ApiModelProperty("定位(三维)文本表示")
    @TableField(exist = false)
    private String geom3Text;

}