package com.seali.bridge.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmRiskProtect对象", description = "防护目标信息表")
@TableName(value = "usm_risk_protect", autoResultMap = true)
public class UsmRiskProtect extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("防护目标编码")
    @TableField("protect_code")
    private String protectCode;

    @ApiModelProperty("防护目标名称")
    @TableField("protect_name")
    private String protectName;

    @ApiModelProperty("建筑类型编码（6003501：学校，6003502：医院，6003503：重大基础设施/通讯、科技、体育、文化等社会事业，6003504：人员密集场所/如商场等，6003505：交通枢纽/如车站数据， 6003506：其他）")
    @TableField("building_type")
    private Integer buildingType;

    @ApiModelProperty("建筑类型名称")
    @TableField("building_type_name")
    private String buildingTypeName;

    @ApiModelProperty("是否重点防护目标（0：否，1：是）")
    @TableField("is_major")
    private String isMajor;

    @ApiModelProperty("建筑面积")
    @TableField("building_area")
    private String buildingArea;

    @ApiModelProperty("满负荷人数")
    @TableField("full_people_number")
    private Integer fullPeopleNumber;

    @ApiModelProperty("建筑年份")
    @TableField("building_year")
    private String buildingYear;

    @ApiModelProperty("所属单位编码")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("所属单位编码名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("联系人")
    @TableField("contact_user")
    private String contactUser;

    @ApiModelProperty("联系电话")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;

}