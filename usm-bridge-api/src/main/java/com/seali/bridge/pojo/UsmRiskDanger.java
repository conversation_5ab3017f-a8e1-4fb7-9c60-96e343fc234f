package com.seali.bridge.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmRiskDanger对象", description = "危险源信息表")
@TableName(value = "usm_risk_danger", autoResultMap = true)
public class UsmRiskDanger extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("危险源编码")
    @TableField("danger_code")
    private String dangerCode;

    @ApiModelProperty("危险源名称")
    @TableField("danger_name")
    private String dangerName;

    @ApiModelProperty("建筑类型编码（6003401：危险化学品工厂，6003402：饭店，6003403：锅炉站，6003404：放射源，6003405：加气站，6003406：加油站，6003407：其他）")
    @TableField("building_type")
    private Integer buildingType;

    @ApiModelProperty("建筑类型名称")
    @TableField("building_type_name")
    private String buildingTypeName;

    @ApiModelProperty("是否重大危险源（0：否，1：是）")
    @TableField("is_major")
    private String isMajor;

    @ApiModelProperty("特征描述")
    @TableField("feature_desc")
    private String featureDesc;

    @ApiModelProperty("风险等级")
    @TableField("risk_level_name")
    private String riskLevelName;

    @ApiModelProperty("影响半径（KM）")
    @TableField("influence_radius")
    private BigDecimal influenceRadius;

    @ApiModelProperty("财产损失")
    @TableField("property_loss")
    private String propertyLoss;

    @ApiModelProperty("所属单位编码")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("所属单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("联系人")
    @TableField("contact_user")
    private String contactUser;

    @ApiModelProperty("联系电话")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

}