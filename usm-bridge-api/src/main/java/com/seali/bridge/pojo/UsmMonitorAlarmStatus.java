package com.seali.bridge.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorAlarmStatus对象", description = "报警状态表")
@TableName(value = "usm_monitor_alarm_status", autoResultMap = true)
public class UsmMonitorAlarmStatus extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("报警ID")
    @TableField("alarm_id")
    private String alarmId;

    @ApiModelProperty("报警状态（4001901:发生报警, 4001902:确认报警, 4001903:处置报警）")
    @TableField("alarm_status")
    private Integer alarmStatus;

    @ApiModelProperty("报警状态名称")
    @TableField("alarm_status_name")
    private String alarmStatusName;

    @ApiModelProperty("描述")
    @TableField("description")
    private String description;

    @ApiModelProperty("单位")
    @TableField("unit")
    private String unit;

    @ApiModelProperty("图片地址")
    @TableField("pic_urls")
    private String picUrls;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;

    @ApiModelProperty("确认结果（4002001：真实报警，4002002：误报）")
    @TableField("confirm_result")
    private Integer confirmResult;

    @ApiModelProperty("确认结果名称")
    @TableField("confirm_result_name")
    private String confirmResultName;

    @ApiModelProperty("处置状态（4002101：处置中，4002102：处置完成）")
    @TableField("handle_status")
    private Integer handleStatus;

    @ApiModelProperty("处置状态名称")
    @TableField("handle_status_name")
    private String handleStatusName;

    @ApiModelProperty("处置人")
    @TableField("handle_user")
    private String handleUser;

}