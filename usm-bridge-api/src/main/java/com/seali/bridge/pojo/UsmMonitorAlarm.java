package com.seali.bridge.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorAlarm对象", description = "设备报警表")
@TableName(value = "usm_monitor_alarm", autoResultMap = true)
public class UsmMonitorAlarm extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("报警设备ID")
    @TableField("device_id")
    private String deviceId;

    @ApiModelProperty("报警来源")
    @TableField("alarm_source")
    private String alarmSource;

    @ApiModelProperty("报警编号")
    @TableField("alarm_code")
    private String alarmCode;

    @ApiModelProperty("报警时间")
    @TableField("alarm_time")
    private Timestamp alarmTime;

    @ApiModelProperty("监测对象ID")
    @TableField("monitor_object_id")
    private String monitorObjectId;

    @ApiModelProperty("监测对象名称")
    @TableField("monitor_object_name")
    private String monitorObjectName;

    @ApiModelProperty("报警值")
    @TableField("alarm_value")
    private String alarmValue;

    @ApiModelProperty("报警位置")
    @TableField("alarm_location")
    private String alarmLocation;

    @ApiModelProperty("报警级别（9101:一级, 9102:二级, 9103:三级, 9104:四级）")
    @TableField("alarm_level")
    private Integer alarmLevel;

    @ApiModelProperty("报警级别名称")
    @TableField("alarm_level_name")
    private String alarmLevelName;

    @ApiModelProperty("报警状态（9201:待确认, 9202:误报, 9203:待处置, 9204:处置中, 9205:已处置, 9206:已归档）")
    @TableField("alarm_status")
    private Integer alarmStatus;

    @ApiModelProperty("报警状态名称")
    @TableField("alarm_status_name")
    private String alarmStatusName;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;

    @ApiModelProperty("设备监测数据Id：如usm_fixed_point_laser_methane_monitor的id")
    @TableField("monitor_data_id")
    private String monitorDataId;

    @ApiModelProperty("5004001-风速,5004002-风向,5004003-温度,5004004-湿度,5004005-地震,5004006-车重、轴重,5004007-车速,5004008-位移、变形,5004009-XY倾角,5004010-应变,5004011-支座反力,5004012-索力,5004013-车船撞击,5004014-支座偏位,5004015-裂缝,5004016-X向加速度,5004017-Y向加速度,5004018-Z向加速度")
    @TableField("monitor_index")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    @TableField("monitor_index_name")
    private String monitorIndexName;

    @ApiModelProperty("报警值单位")
    @TableField("alarm_value_unit")
    private String alarmValueUnit;

}