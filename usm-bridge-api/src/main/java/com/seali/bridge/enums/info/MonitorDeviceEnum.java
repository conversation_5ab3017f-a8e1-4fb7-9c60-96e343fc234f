package com.seali.bridge.enums.info;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备监测相关枚举
 */
public class MonitorDeviceEnum {

    /**
     * 监测状态枚举
     */
//    @Getter
//    @AllArgsConstructor
//    public enum MonitorStatus implements BaseEnum {
//        ONLINE(9001, "在线"),
//        OFFLINE(9002, "离线");
//
//        private final int code;
//        private final String name;
//    }

    /**
     * 报警级别枚举
     * 报警级别（4001701:一级, 4001702:二级, 4001703:三级）
     */
    @Getter
    @AllArgsConstructor
    public enum AlarmLevel implements BaseEnum {
        LEVEL_ONE(4001701, "一级"),
        LEVEL_TWO(4001702, "二级"),
        LEVEL_THREE(4001703, "三级");

        private final int code;
        private final String name;
    }

    /**
     * 报警状态枚举
     * 报警状态（4001801:待确认, 4001802:误报, 4001803:待处置, 4001804:处置中, 4001805:已处置, 4001806:已归档）
     */
    @Getter
    @AllArgsConstructor
    public enum AlarmStatus implements BaseEnum {
        PENDING_CONFIRM(4001801, "待确认"),
        FALSE_ALARM(4001802, "误报"),
        PENDING_HANDLE(4001803, "待处置"),
        HANDLING(4001804, "处置中"),
        HANDLED(4001805, "已处置"),
        ARCHIVED(4001806, "已归档");

        private final int code;
        private final String name;
    }

    /**
     * 报警处置流程状态枚举
     * 报警状态（4001901:发生报警, 4001902:确认报警, 4001903:处置报警）
     */
    @Getter
    @AllArgsConstructor
    public enum AlarmHandleProcessStatus implements BaseEnum {
        HAPPEN(4001901, "发生报警"),
        CONFIRM(4001902, "确认报警"),
        HANDLE(4001903, "处置报警");

        private final int code;
        private final String name;
    }

    /**
     * 报警确认状态枚举
     * 确认结果（4002001:真实报警，4002002:误报）
     */
    @Getter
    @AllArgsConstructor
    public enum AlarmConfirmStatus implements BaseEnum {
        TRUE_ALARM(4002001, "真实报警"),
        FALSE_ALARM(4002002, "误报");

        private final int code;
        private final String name;
    }

    /**
     * 报警处置状态枚举
     * 处置状态（4002101:处置中，4002102:处置完成）
     */
    @Getter
    @AllArgsConstructor
    public enum AlarmHandleStatus implements BaseEnum {
        HANDLING(4002101, "处置中"),
        HANDLED(4002102, "处置完成");

        private final int code;
        private final String name;
    }
}