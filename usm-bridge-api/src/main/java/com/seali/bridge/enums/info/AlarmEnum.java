package com.seali.bridge.enums.info;

import com.seali.bridge.entity.response.AlarmTypeResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;

public class AlarmEnum {

    /**
     * 报警类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum AlarmType implements BaseEnum {
        WD(5004003, "温度监测报警"),
        SD(5004004, "湿度监测报警"),
        WYBX(5004008, "位移/变形监测报警"),
        YB(5004010, "应变监测报警"),
        LF(5004015, "裂缝监测报警"),
        ND(5004019, "挠度监测报警");

        private final int code;
        private final String name;

        public AlarmTypeResponse toDTO() {
            return new AlarmTypeResponse(this.code, this.name);
        }

        public static String getNameByCode(int code) {
            for (AlarmType type : AlarmType.values()) {
                if (type.getCode() == code) {
                    return type.getName();
                }
            }
            return null;
        }
    }


}
