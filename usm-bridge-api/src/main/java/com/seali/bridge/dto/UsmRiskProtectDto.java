package com.seali.bridge.dto;

import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmRiskProtectDto", description = "防护目标信息表数据传输对象")
public class UsmRiskProtectDto extends BaseDto {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("防护目标编码")
    private String protectCode;

    @ApiModelProperty("防护目标名称")
    private String protectName;

    @ApiModelProperty("建筑类型编码（6003501：学校，6003502：医院，6003503：重大基础设施/通讯、科技、体育、文化等社会事业，6003504：人员密集场所/如商场等，6003505：交通枢纽/如车站数据， 6003506：其他）")
    private Integer buildingType;

    @ApiModelProperty("建筑类型名称")
    private String buildingTypeName;

    @ApiModelProperty("是否重点防护目标（0：否，1：是）")
    private String isMajor;

    @ApiModelProperty("建筑面积")
    private String buildingArea;

    @ApiModelProperty("满负荷人数")
    private Integer fullPeopleNumber;

    @ApiModelProperty("建筑年份")
    private String buildingYear;

    @ApiModelProperty("所属单位编码")
    private String managementUnit;

    @ApiModelProperty("所属单位编码名称")
    private String managementUnitName;

    @ApiModelProperty("联系人")
    private String contactUser;

    @ApiModelProperty("联系电话")
    private String contactInfo;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("区县编码")
    private String county;

    @ApiModelProperty("区县名称")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    private String townName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;

}