package com.seali.bridge.dto;

import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorAlarmStatusDto", description = "报警状态表数据传输对象")
public class UsmMonitorAlarmStatusDto extends BaseDto {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("报警ID")
    private String alarmId;

    @ApiModelProperty("报警状态（4001901:发生报警, 4001902:确认报警, 4001903:处置报警）")
    private Integer alarmStatus;

    @ApiModelProperty("报警状态名称")
    private String alarmStatusName;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("图片地址")
    private String picUrls;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty("确认结果（4002001：真实报警，4002002：误报）")
    private Integer confirmResult;

    @ApiModelProperty("确认结果名称")
    private String confirmResultName;

    @ApiModelProperty("处置状态（4002101：处置中，4002102：处置完成）")
    private Integer handleStatus;

    @ApiModelProperty("处置状态名称")
    private String handleStatusName;

    @ApiModelProperty("处置人")
    private String handleUser;

}