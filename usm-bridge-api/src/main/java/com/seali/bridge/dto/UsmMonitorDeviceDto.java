package com.seali.bridge.dto;

import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorDeviceDto", description = "供热设备信息数据传输对象")
public class UsmMonitorDeviceDto extends BaseDto {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("设备唯一标识")
    private String dataId;

    @ApiModelProperty("设备编码")
    private String indexCode;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备类型编码（2001301：管网流量，2001302：管网热量，2001303：管网温度，2001304：用户室温，2001305：管网压力）")
    private String deviceType;

    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;

    @ApiModelProperty("监测类型")
    private String monitorType;

    @ApiModelProperty("监测类型名称")
    private String monitorTypeName;

    @ApiModelProperty("监测指标编码（2001401：管网流量监测（m3/h），2001402：管网热量监测（热量：MW），2001405：管网温度监测（温度：℃），2001406：用户室温监测（温度：℃），2001407：管网压力监测（压力：Mpa））")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    private String monitorIndexName;

    @ApiModelProperty("监测对象编码（2001501：管线，2001502：热源，2001503：换热站，2001504：用户，2001505：窨井）")
    private String monitorTarget;

    @ApiModelProperty("监测对象名称")
    private String monitorTargetName;

    @ApiModelProperty("监测对象id（管网、场站、窨井）")
    private String monitorObjectId;

    @ApiModelProperty("监测对象名称")
    private String monitorObjectName;

    @ApiModelProperty("采集频率(次/分钟)")
    private BigDecimal collectFrequency;

    @ApiModelProperty("上传频率(次/分钟)")
    private BigDecimal uploadFrequency;

    @ApiModelProperty("量程下线")
    private String measureRangeLow;

    @ApiModelProperty("量程上线")
    private String measureRangeUp;

    @ApiModelProperty("量程单位")
    private String measureUnit;

    @ApiModelProperty("区域编码")
    private String regionCode;

    @ApiModelProperty("区域名称")
    private String regionName;

    @ApiModelProperty("区域Path")
    private String regionPath;

    @ApiModelProperty("区域全称")
    private String regionPathName;

    @ApiModelProperty("安装地址")
    private String address;

    @ApiModelProperty("空间坐标文本表示")
    private String geomText;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("在线状态（0：离线，1：在线）")
    private Integer onlineStatus;

    @ApiModelProperty("有视频能力")
    private String isVss;

    @ApiModelProperty("图片URL列表")
    private String picUrls;

    @ApiModelProperty("数据更新时间")
    private Timestamp time;

    @ApiModelProperty("权属单位Code")
    private String ownershipUnit;

    @ApiModelProperty("权属单位名称")
    private String ownershipUnitName;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("空间坐标文本表示")
    private String geom3Text;

    @ApiModelProperty("桥梁名称")
    private String bridgeName;
}