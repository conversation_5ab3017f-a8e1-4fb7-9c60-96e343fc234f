package com.seali.bridge.dto;

import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorIndicatorsDto", description = "监测指标表数据传输对象")
public class UsmMonitorIndicatorsDto extends BaseDto {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("设备器类型，对应usm_monitor_device.device_type")
    private String deviceType;

    @ApiModelProperty("5004001-风速,5004002-风向,5004003-温度,5004004-湿度,5004005-地震,5004006-车重、轴重,5004007-车速,5004008-位移、变形,5004009-XY倾角,5004010-应变,5004011-支座反力,5004012-索力,5004013-车船撞击,5004014-支座偏位,5004015-裂缝,5004016-X向加速度,5004017-Y向加速度,5004018-Z向加速度")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    private String monitorIndexName;

    @ApiModelProperty("量程下线")
    private String measureRangeLow;

    @ApiModelProperty("量程上线")
    private String measureRangeUp;

    @ApiModelProperty("量程单位")
    private String measureUnit;

    @ApiModelProperty("监测字段，如检测的是浓度，填ld，对应usm_monitor_record.ld字段")
    private String monitorField;

    @ApiModelProperty("类型：0-监测指标是状态，1-监测指标是数值")
    private Integer type;

    @ApiModelProperty("设备器类型名称")
    private String deviceTypeName;

}