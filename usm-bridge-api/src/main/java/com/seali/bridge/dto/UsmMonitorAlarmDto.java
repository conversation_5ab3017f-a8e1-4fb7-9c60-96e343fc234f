package com.seali.bridge.dto;

import com.seali.common.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorAlarmDto", description = "设备报警表数据传输对象")
public class UsmMonitorAlarmDto extends BaseDto {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("报警设备ID")
    private String deviceId;

    @ApiModelProperty("报警来源")
    private String alarmSource;

    @ApiModelProperty("报警编号")
    private String alarmCode;

    @ApiModelProperty("报警时间")
    private Timestamp alarmTime;

    @ApiModelProperty("监测对象ID")
    private String monitorObjectId;

    @ApiModelProperty("监测对象名称")
    private String monitorObjectName;

    @ApiModelProperty("报警值")
    private String alarmValue;

    @ApiModelProperty("报警位置")
    private String alarmLocation;

    @ApiModelProperty("报警级别（9101:一级, 9102:二级, 9103:三级, 9104:四级）")
    private Integer alarmLevel;

    @ApiModelProperty("报警级别名称")
    private String alarmLevelName;

    @ApiModelProperty("报警状态（9201:待确认, 9202:误报, 9203:待处置, 9204:处置中, 9205:已处置, 9206:已归档）")
    private Integer alarmStatus;

    @ApiModelProperty("报警状态名称")
    private String alarmStatusName;

    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty("设备监测数据Id：如usm_fixed_point_laser_methane_monitor的id")
    private String monitorDataId;

    @ApiModelProperty("持续时长")
    private String duration;

    @ApiModelProperty("5004001-风速,5004002-风向,5004003-温度,5004004-湿度,5004005-地震,5004006-车重、轴重,5004007-车速,5004008-位移、变形,5004009-XY倾角,5004010-应变,5004011-支座反力,5004012-索力,5004013-车船撞击,5004014-支座偏位,5004015-裂缝,5004016-X向加速度,5004017-Y向加速度,5004018-Z向加速度")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    private String monitorIndexName;

    @ApiModelProperty("报警值单位")
    private String alarmValueUnit;

    @ApiModelProperty("最高报警等级")
    private String maxAlarmLevel;

    @ApiModelProperty("报警类型")
    private Integer alarmType;

    @ApiModelProperty("报警类型名称")
    private String alarmTypeName;

}