FROM openjdk:8-jdk-alpine

# 设置工作目录
WORKDIR /app

# 设置时区为亚洲/上海
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone \

# 创建应用目录结构
RUN mkdir -p lib config

# 复制应用文件
COPY build/libs/usm-gas-service-1.0.0.jar app.jar
COPY build/lib/* lib/
COPY build/config/* config/

# 设置环境变量
ENV TZ=Asia/Shanghai
#ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"
#ENV SPRING_PROFILES_ACTIVE="prod"

ENV CONFIG_PATH=/app/config
ENV PATH="/app:${PATH}"

# 暴露应用端口（根据实际情况调整）
#EXPOSE 8080

# 启动命令
ENTRYPOINT ["java", "-cp", "app.jar:lib/*", "-Dconfig.path=/app/config", "com.seali.gas.GasServiceApplication"]
