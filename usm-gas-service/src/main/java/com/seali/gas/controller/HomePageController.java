package com.seali.gas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.gas.dto.UsmZyGasPipelineDto;
import com.seali.gas.entity.response.HomePageResponse;
import com.seali.gas.entity.response.risk.monitor.AlarmHighFrequencyAlarmDeviceResponse;
import com.seali.gas.service.HomePageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/homePage")
@Api(tags = "首页接口")
@RequiredArgsConstructor
public class HomePageController {

    private final HomePageService homePageService;

    @ApiOperation("1.管线压力级别统计")
    @GetMapping("/pipeline/statistics")
    public UsmZyGasPipelineDto pipelineStatisticsByPressure() {
        return homePageService.pipelineStatisticsByPressure();
    }

    @ApiOperation("2.今日、本月报警数量统计")
    @GetMapping("/alarm/count")
    public HomePageResponse alarmCount() {
        return homePageService.alarmCount();
    }

    @ApiOperation("3.待处置报警统计")
    @GetMapping("/alarm/unhandleStatistics")
    public HomePageResponse alarmStatisticsByLevel(@Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
                                                   @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        return homePageService.alarmStatisticsByLevel(pageNum, pageSize);
    }

    @ApiOperation("4.近30日燃气泄漏报警排名")
    @GetMapping("/alarm/rank")
    public Page<AlarmHighFrequencyAlarmDeviceResponse> alarmRank(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        return homePageService.alarmRank(pageNum, pageSize);
    }

    @ApiOperation("5.报警统计")
    @GetMapping("/alarm/statistics")
    public HomePageResponse alarmStatisticsByTime(@Parameter(description = "天数") @RequestParam(defaultValue = "7") Integer dayIndex) {
        return homePageService.alarmStatisticsByTime(dayIndex);
    }
}
