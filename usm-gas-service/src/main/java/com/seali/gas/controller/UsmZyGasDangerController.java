package com.seali.gas.controller;

import com.seali.gas.pojo.UsmZyGasDanger;
import com.seali.gas.dto.UsmZyGasDangerDto;
import com.seali.gas.form.UsmZyGasDangerForm;
import com.seali.gas.service.UsmZyGasDangerService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmZyGasDanger")
@Api(tags = "资源.7.危险源信息表接口")
public class UsmZyGasDangerController extends BaseController<UsmZyGasDangerService, UsmZyGasDanger, UsmZyGasDangerForm, UsmZyGasDangerDto, Long> {

    private UsmZyGasDangerService usmZyGasDangerService;

    @Autowired
    public UsmZyGasDangerController(UsmZyGasDangerService service) {
        super(service);
        this.usmZyGasDangerService = service;
    }
}