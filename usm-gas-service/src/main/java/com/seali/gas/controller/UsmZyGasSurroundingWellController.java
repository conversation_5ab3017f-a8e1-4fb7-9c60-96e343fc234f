package com.seali.gas.controller;

import com.seali.gas.pojo.UsmZyGasSurroundingWell;
import com.seali.gas.dto.UsmZyGasSurroundingWellDto;
import com.seali.gas.form.UsmZyGasSurroundingWellForm;
import com.seali.gas.service.UsmZyGasSurroundingWellService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmZyGasSurroundingWell")
@Api(tags = "资源.4.1.燃气周边相邻空间窨井信息表接口")
public class UsmZyGasSurroundingWellController extends BaseController<UsmZyGasSurroundingWellService, UsmZyGasSurroundingWell, UsmZyGasSurroundingWellForm, UsmZyGasSurroundingWellDto, Long> {

    private UsmZyGasSurroundingWellService usmZyGasSurroundingWellService;

    @Autowired
    public UsmZyGasSurroundingWellController(UsmZyGasSurroundingWellService service) {
        super(service);
        this.usmZyGasSurroundingWellService = service;
    }
}