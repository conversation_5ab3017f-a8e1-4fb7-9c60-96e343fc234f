package com.seali.gas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.common.controller.BaseController;
import com.seali.gas.dto.UsmMonitorRecordDto;
import com.seali.gas.entity.request.MonitorRecordRequest;
import com.seali.gas.entity.response.DeviceOfflineRecordResponse;
import com.seali.gas.form.UsmMonitorRecordForm;
import com.seali.gas.pojo.UsmMonitorRecord;
import com.seali.gas.service.UsmMonitorRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/usmMonitorRecord")
@Api(tags = "监测.2.设备监测记录表接口")
public class UsmMonitorRecordController extends BaseController<UsmMonitorRecordService, UsmMonitorRecord, UsmMonitorRecordForm, UsmMonitorRecordDto, Long> {

    private UsmMonitorRecordService usmMonitorRecordService;

    @Autowired
    public UsmMonitorRecordController(UsmMonitorRecordService service) {
        super(service);
        this.usmMonitorRecordService = service;
    }

    @ApiOperation(value = "设备监测曲线查询",notes = "设备监测曲线查询方法详情" )
    @PostMapping("/monitorCurve")
    public List<UsmMonitorRecordDto> monitorCurve(@RequestBody MonitorRecordRequest request){
        return usmMonitorRecordService.monitorCurve(request);
    }

    @ApiOperation(value = "监测历史记录-在线(分页)",notes = "监测历史记录-在线" )
    @PostMapping("/monitorCurvePage/{pageNum}/{pageSize}")
    public Page<UsmMonitorRecordDto> monitorCurvePage(
            @Parameter(description = "页码", example = "1") @PathVariable("pageNum") int pageNum,
            @Parameter(description = "每页大小", example = "10") @PathVariable("pageSize") int pageSize,
            @RequestBody MonitorRecordRequest request){
        return usmMonitorRecordService.monitorCurvePage(pageNum, pageSize, request);
    }

    @ApiOperation(value = "设备离线记录查询(分页)",notes = "查询设备离线记录，包含离线时间、恢复时间、离线时长" )
    @PostMapping("/offlineRecords/{pageNum}/{pageSize}")
    public Page<DeviceOfflineRecordResponse> queryDeviceOfflineRecords(
            @Parameter(description = "页码", example = "1") @PathVariable("pageNum") int pageNum,
            @Parameter(description = "每页大小", example = "10") @PathVariable("pageSize") int pageSize,
            @RequestBody MonitorRecordRequest request){
        return usmMonitorRecordService.queryDeviceOfflineRecords(pageNum, pageSize, request);
    }
}