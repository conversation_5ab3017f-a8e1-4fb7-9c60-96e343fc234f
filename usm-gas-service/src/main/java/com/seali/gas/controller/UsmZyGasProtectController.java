package com.seali.gas.controller;

import com.seali.gas.pojo.UsmZyGasProtect;
import com.seali.gas.dto.UsmZyGasProtectDto;
import com.seali.gas.form.UsmZyGasProtectForm;
import com.seali.gas.service.UsmZyGasProtectService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmZyGasProtect")
@Api(tags = "资源.8.防护目标信息表接口")
public class UsmZyGasProtectController extends BaseController<UsmZyGasProtectService, UsmZyGasProtect, UsmZyGasProtectForm, UsmZyGasProtectDto, Long> {

    private UsmZyGasProtectService usmZyGasProtectService;

    @Autowired
    public UsmZyGasProtectController(UsmZyGasProtectService service) {
        super(service);
        this.usmZyGasProtectService = service;
    }
}