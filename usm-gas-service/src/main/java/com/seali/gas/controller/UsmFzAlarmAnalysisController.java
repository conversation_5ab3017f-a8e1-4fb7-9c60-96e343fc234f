package com.seali.gas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.gas.dto.UsmFzAlarmAnalysisDto;
import com.seali.gas.entity.response.risk.monitor.*;
import com.seali.gas.form.UsmFzAlarmAnalysisForm;
import com.seali.gas.service.UsmFzAlarmAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 辅助决策-燃气泄露报警统计分析Controller
 * 提供报警统计、报警等级统计、报警趋势分析等功能
 */
@Api(tags = "决策.3.燃气泄露报警统计分析")
@RestController
@RequestMapping("/usmFzAlarmAnalysis")
@RequiredArgsConstructor
public class UsmFzAlarmAnalysisController {

    private final UsmFzAlarmAnalysisService usmFzAlarmAnalysisService;

    /**
     * 获取监测报警统计信息
     * 包括报警总数、待处置、已处置报警数等
     */
    @ApiOperation("1.监测报警统计")
    @PostMapping("/statistics")
    public UsmFzAlarmAnalysisDto getAlarmStatistics(@RequestBody UsmFzAlarmAnalysisForm request) {
        return usmFzAlarmAnalysisService.getAlarmStatistics(request);
    }

    /**
     * 处置、误报统计
     * 包括处置完成率、误报率、平均处置时长等
     */
    @ApiOperation("2.处置、误报统计")
    @PostMapping("/statistics/disposalSituation")
    public AlarmDisposalSituationResponse getAlarmDisposalSituation(@RequestBody UsmFzAlarmAnalysisForm request) {
        return usmFzAlarmAnalysisService.getAlarmDisposalSituation(request);
    }

    /**
     * 获取报警趋势分析统计信息
     * 统计指定日期范围内的报警趋势
     */
    @ApiOperation("3.报警趋势分析统计")
    @PostMapping("/trend/statistics")
    public AlarmTrendStatisticsResponse getAlarmTrendStatistics(@RequestBody UsmFzAlarmAnalysisForm request) {
        return usmFzAlarmAnalysisService.getAlarmTrendStatistics(request);
    }

    /**
     * 获取报警等级统计信息
     * 按不同报警等级统计报警数量
     */
    @ApiOperation("4.报警等级统计")
    @PostMapping("/level/statistics")
    public AlarmLevelStatisticsResponse getAlarmLevelStatistics(@RequestBody UsmFzAlarmAnalysisForm request) {
        return usmFzAlarmAnalysisService.getAlarmLevelCounts(request);
    }

    /**
     * 获取高发报警设备信息
     * 统计指定日期范围内报警频率较高的设备
     */
    @ApiOperation("5.高发报警设备")
    @PostMapping("/device/high-frequency")
    public Page<AlarmHighFrequencyAlarmDeviceResponse> getHighFrequencyAlarmDevice(
            @RequestBody UsmFzAlarmAnalysisForm request,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        return usmFzAlarmAnalysisService.getHighFrequencyAlarmDevice(request, pageNum, pageSize);
    }

    /**
     * 获取企业报警信息统计
     * 统计指定日期范围内企业报警信息
     */
    @ApiOperation("6.企业报警信息统计")
    @PostMapping("/enterprise/statistics")
    public Page<AlarmEnterPriseStatisticsResponse> getEnterpriseStatistics(
            @RequestBody UsmFzAlarmAnalysisForm request,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        return usmFzAlarmAnalysisService.getEnterpriseStatistics(request, pageNum, pageSize);
    }
} 