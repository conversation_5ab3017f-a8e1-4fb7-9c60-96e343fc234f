package com.seali.gas.controller;

import com.seali.gas.pojo.UsmSecurityReport;
import com.seali.gas.dto.UsmSecurityReportDto;
import com.seali.gas.form.UsmSecurityReportForm;
import com.seali.gas.service.UsmSecurityReportService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmSecurityReport")
@Api(tags = "决策.4.燃气安全运行评估报告接口")
public class UsmSecurityReportController extends BaseController<UsmSecurityReportService, UsmSecurityReport, UsmSecurityReportForm, UsmSecurityReportDto, Long> {

    private UsmSecurityReportService usmSecurityReportService;

    @Autowired
    public UsmSecurityReportController(UsmSecurityReportService service) {
        super(service);
        this.usmSecurityReportService = service;
    }
}