package com.seali.gas.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.common.util.NumberUtil;
import com.seali.common.util.TimeUtil;
import com.seali.gas.entity.request.ConditionRequest;
import com.seali.gas.entity.response.risk.monitor.*;
import com.seali.gas.dao.MonitorAlarmAnalysisMapper;
import com.seali.gas.service.MonitorAlarmAnalysisService;
import com.seali.gas.util.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 监测报警分析服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MonitorAlarmAnalysisServiceImpl implements MonitorAlarmAnalysisService {

    private final MonitorAlarmAnalysisMapper monitorAlarmAnalysisMapper;

    @Override
    public AlarmMonitorDeviceStatisticsResponse getMonitorDeviceStatistics() {
        log.info("获取监测设备统计信息");
        AlarmMonitorDeviceStatisticsResponse response = new AlarmMonitorDeviceStatisticsResponse();
        
        // 获取设备总数和在线设备数
        Integer totalDevices = monitorAlarmAnalysisMapper.getTotalDevices();
        Integer onlineDevices = monitorAlarmAnalysisMapper.getOnlineDevices();
        
        // 计算在线率
        Double onlineRate = NumberUtil.percentage(onlineDevices, totalDevices);

        // 设置响应数据
        response.setTotalCount(totalDevices);
        response.setOfflineCount(totalDevices - onlineDevices);
        response.setOnlineRate(onlineRate);
        
        // 获取设备类型统计
        List<AlarmMonitorDeviceStatisticsResponse.DeviceTypeStatistics> deviceTypeStatistics =
                monitorAlarmAnalysisMapper.getDeviceTypeStatistics();
        response.setDeviceTypeStatistics(deviceTypeStatistics);
        
        return response;
    }

    @Override
    public AlarmStatisticsResponse getAlarmStatistics(Integer dayIndex, Integer pageNum, Integer pageSize) {
        log.info("获取监测报警统计信息, 天数: {}, 分页参数: pageNum-{}, pageSize-{}", dayIndex, pageNum, pageSize);
        AlarmStatisticsResponse response = new AlarmStatisticsResponse();
        
        // 获取报警总数和已处理报警数
        Integer totalAlarms = monitorAlarmAnalysisMapper.getTotalAlarms();
        Integer handledAlarms = monitorAlarmAnalysisMapper.getHandledAlarms();
        Integer processingAlarms = monitorAlarmAnalysisMapper.getProcessingAlarms();

        // 设置响应数据
        response.setTotalCount(totalAlarms);
        response.setHandledCount(handledAlarms);
        response.setProcessingCount(processingAlarms);
        response.setUnhandledCount(totalAlarms - handledAlarms - processingAlarms);

        Page<AlarmStatisticsResponse.AlarmInfo> page = new Page<>(pageNum, pageSize);

        // 获取报警趋势（最近30天）
        ConditionRequest request = new ConditionRequest();
        LocalDateTime end = TimeUtil.now();
        LocalDateTime start = TimeUtil.minusDays(end, dayIndex);
        request.setStartDate(TimeUtil.localDateTimeToDate(start));
        request.setEndDate(TimeUtil.localDateTimeToDate(end));
        Page<AlarmStatisticsResponse.AlarmInfo> alarmInfoPage = page.setRecords(monitorAlarmAnalysisMapper.getAlarmTrends(request));
        response.setAlarms(alarmInfoPage);

        return response;
    }

    @Override
    public Page<AlarmStatisticsResponse.AlarmInfo> getAlarmStatisticsCondition(ConditionRequest request, Integer pageNum, Integer pageSize) {
        Page<AlarmStatisticsResponse.AlarmInfo> page = new Page<>(pageNum, pageSize);
        return page.setRecords(monitorAlarmAnalysisMapper.getAlarmTrends(request));
    }

    @Override
    public AlarmLevelStatisticsResponse getAlarmLevelCounts(Integer dayIndex) {
        log.info("获取报警等级统计信息, 天数: {}", dayIndex);
        AlarmLevelStatisticsResponse response = new AlarmLevelStatisticsResponse();
        
        // 获取各等级报警数量
        List<AlarmLevelStatisticsResponse.AlarmLevelStatistics> alarmLevelStatistics = 
                monitorAlarmAnalysisMapper.getAlarmLevelCounts(dayIndex);
        response.setStatistics(alarmLevelStatistics);
        
        return response;
    }

    @Override
    public AlarmTrendStatisticsResponse getAlarmTrendStatistics(Integer dayIndex) {
        log.info("获取报警趋势分析统计信息, 天数: {}", dayIndex);
        AlarmTrendStatisticsResponse response = new AlarmTrendStatisticsResponse();
        
        // 获取报警趋势数据
        List<AlarmTrendStatisticsResponse.DailyAlarmStatistics> alarmTrendData = 
                monitorAlarmAnalysisMapper.getAlarmTrendData(dayIndex);
        response.setStatistics(alarmTrendData);
        
        return response;
    }

    @Override
    public Page<AlarmHighFrequencyAlarmDeviceResponse> getHighFrequencyAlarmDevice(
            Integer dayIndex, Integer pageNum, Integer pageSize) {
        log.info("获取高发报警设备信息, 天数: {}, 页码: {}, 每页大小: {}",
                dayIndex, pageNum, pageSize);

        Page<AlarmHighFrequencyAlarmDeviceResponse> page = new Page<>(pageNum, pageSize);

        return page.setRecords(monitorAlarmAnalysisMapper.getHighFrequencyAlarmDevices(dayIndex));
    }
} 