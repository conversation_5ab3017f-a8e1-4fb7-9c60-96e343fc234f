package com.seali.gas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.gas.dto.UsmFzAlarmAnalysisDto;
import com.seali.gas.entity.response.risk.monitor.*;
import com.seali.gas.form.UsmFzAlarmAnalysisForm;

/**
 * 辅助决策-燃气泄露报警统计分析服务接口
 * 提供报警统计、报警等级统计、报警趋势分析等功能
 */
public interface UsmFzAlarmAnalysisService {

    /**
     * 获取监测报警统计信息
     * 包括报警总数、待处置、已处置报警数等
     *
     * @param  request
     * @return 报警统计响应
     */
    UsmFzAlarmAnalysisDto getAlarmStatistics(UsmFzAlarmAnalysisForm request);

    /**
     * 处置、误报统计
     * 包括处置完成率、误报率、平均处置时长等
     *
     * @param  request 分页参数
     * @return 报警统计响应
     */
    AlarmDisposalSituationResponse getAlarmDisposalSituation(UsmFzAlarmAnalysisForm request);

    /**
     * 获取报警等级统计信息
     * 按不同报警等级统计报警数量
     *
     * @param request 日期范围
     * @return 报警等级统计响应
     */
    AlarmLevelStatisticsResponse getAlarmLevelCounts(UsmFzAlarmAnalysisForm request);

    /**
     * 获取报警趋势分析统计信息
     * 统计指定日期范围内的报警趋势
     *
     * @param request 日期范围
     * @return 报警趋势统计响应
     */
    AlarmTrendStatisticsResponse getAlarmTrendStatistics(UsmFzAlarmAnalysisForm request);

    /**
     * 获取高发报警设备信息
     * 统计指定日期范围内报警频率较高的设备
     *
     * @param request 日期范围
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 高发报警设备响应
     */
    Page<AlarmHighFrequencyAlarmDeviceResponse> getHighFrequencyAlarmDevice(UsmFzAlarmAnalysisForm request, Integer pageNum, Integer pageSize);

    /**
     * 获取企业报警信息统计
     * @param request
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<AlarmEnterPriseStatisticsResponse> getEnterpriseStatistics(UsmFzAlarmAnalysisForm request, Integer pageNum, Integer pageSize);
}