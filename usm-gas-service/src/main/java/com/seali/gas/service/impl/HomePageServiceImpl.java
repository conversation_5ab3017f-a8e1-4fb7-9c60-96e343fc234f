package com.seali.gas.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.gas.dao.HomePageMapper;
import com.seali.gas.dto.UsmZyGasPipelineDto;
import com.seali.gas.entity.response.HomePageResponse;
import com.seali.gas.entity.response.risk.monitor.AlarmHighFrequencyAlarmDeviceResponse;
import com.seali.gas.entity.response.risk.monitor.AlarmStatisticsResponse;
import com.seali.gas.service.HomePageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 首页服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HomePageServiceImpl implements HomePageService {

    private final HomePageMapper homePageMapper;

    @Override
    public UsmZyGasPipelineDto pipelineStatisticsByPressure() {
        UsmZyGasPipelineDto dto = homePageMapper.pipelineStatisticsByPressure();
        dto.setRepairCount(homePageMapper.getRepairCount());
        return dto;
    }

    @Override
    public HomePageResponse alarmCount() {
        HomePageResponse response = homePageMapper.alarmCount();
        return response;
    }

    @Override
    public HomePageResponse alarmStatisticsByLevel(Integer pageNum, Integer pageSize) {
        HomePageResponse response = homePageMapper.alarmStatisticsByLevel();
        Page<AlarmStatisticsResponse.AlarmInfo> page = new Page<>(pageNum, pageSize);
        Page<AlarmStatisticsResponse.AlarmInfo> alarmInfoPage = page.setRecords(homePageMapper.getAlarmList());
        response.setAlarmInfoPage(alarmInfoPage);
        return response;
    }

    @Override
    public Page<AlarmHighFrequencyAlarmDeviceResponse> alarmRank(Integer pageNum, Integer pageSize) {
        Page<AlarmHighFrequencyAlarmDeviceResponse> page = new Page<>(pageNum, pageSize);
        Page<AlarmHighFrequencyAlarmDeviceResponse> alarmInfoPage = page.setRecords(homePageMapper.getAlarmRank());
        return alarmInfoPage;
    }

    @Override
    public HomePageResponse alarmStatisticsByTime(Integer dayIndex) {
        HomePageResponse response = homePageMapper.getAlarmStatistics();
        response.setAlarmTrendStatistics(homePageMapper.getAlarmTrendData(dayIndex));
        return response;
    }
}
