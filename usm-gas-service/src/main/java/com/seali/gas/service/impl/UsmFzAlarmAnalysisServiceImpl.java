package com.seali.gas.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.gas.dao.UsmFzAlarmAnalysisMapper;
import com.seali.gas.dto.UsmFzAlarmAnalysisDto;
import com.seali.gas.entity.response.risk.monitor.*;
import com.seali.gas.form.UsmFzAlarmAnalysisForm;
import com.seali.gas.service.UsmFzAlarmAnalysisService;
import com.seali.gas.util.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 辅助决策-燃气泄露报警统计分析服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UsmFzAlarmAnalysisServiceImpl implements UsmFzAlarmAnalysisService {

    private final UsmFzAlarmAnalysisMapper usmFzAlarmAnalysisMapper;

    @Override
    public UsmFzAlarmAnalysisDto getAlarmStatistics(UsmFzAlarmAnalysisForm request) {
        log.info("获取监测报警统计信息, 开始天数: {}, 结束天数: {}", request.getStartDate(), request.getEndDate());
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();

        // 1. 计算当前周期报警数量
        UsmFzAlarmAnalysisDto dto = usmFzAlarmAnalysisMapper.countAlarmsByStatus(startDate, endDate);

        // 2. 计算环比数据(上一周期)
        if (startDate != null && endDate != null){
            long periodMillis = DateUtil.between(startDate, endDate, DateUnit.DAY);
            Date momStart = DateUtil.offset(startDate, DateField.DAY_OF_YEAR, (int)-periodMillis);
            Date momEnd = DateUtil.offset(endDate, DateField.DAY_OF_YEAR, (int)-periodMillis);
            UsmFzAlarmAnalysisDto momCount = usmFzAlarmAnalysisMapper.countAlarmsByStatus(momStart, momEnd);

            // 3. 计算同比数据(去年同期)
            Date yoyStart = DateUtil.offset(startDate, DateField.YEAR, -1);
            Date yoyEnd = DateUtil.offset(endDate, DateField.YEAR, -1);
            UsmFzAlarmAnalysisDto yoyCount = usmFzAlarmAnalysisMapper.countAlarmsByStatus(yoyStart, yoyEnd);

            // 4. 计算变化率和趋势
            // 计算环比变化
            AlarmDisposalSituationResponse.MetricTrendAnalysis momAnalysis = createTrendAnalysis(dto.getTotalAlarms().doubleValue(), momCount.getTotalAlarms().doubleValue());
            dto.setMomAnalysis(percentFormat(momAnalysis.getChangeRate()));
            dto.setMomAnalysisTrend(momAnalysis.getTrend());

            // 计算同比变化
            AlarmDisposalSituationResponse.MetricTrendAnalysis yoyAnalysis = createTrendAnalysis(dto.getTotalAlarms().doubleValue(), yoyCount.getTotalAlarms().doubleValue());
            dto.setYoyAnalysis(percentFormat(yoyAnalysis.getChangeRate()));
            dto.setYoyAnalysisTrend(yoyAnalysis.getTrend());
        }

        Double pendingConfirmRate = calculateRate(dto.getPendingConfirm(), dto.getTotalAlarms());
        Double pendingHandleRate = calculateRate(dto.getPendingHandle(), dto.getTotalAlarms());
        Double handlingRate = calculateRate(dto.getHandling(), dto.getTotalAlarms());
        Double handledRate = 100 - pendingConfirmRate - pendingHandleRate - handlingRate;
        dto.setPendingConfirmRate(percentFormat(pendingConfirmRate));
        dto.setPendingHandleRate(percentFormat(pendingHandleRate));
        dto.setHandlingRate(percentFormat(handlingRate));
        dto.setHandledRate(percentFormat(handledRate));

        return dto;
    }

    private String percentFormat(Double percent){
        return percent == 0 ? "0%" :
                percent % 1 == 0 ? String.format("%.0f%%", percent) :  // 如果是整数
                        String.format("%.2f%%", percent);  // 如果不是整数);
    }

    @Override
    public AlarmDisposalSituationResponse getAlarmDisposalSituation(UsmFzAlarmAnalysisForm request) {

        AlarmDisposalSituationResponse response = new AlarmDisposalSituationResponse();

        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();

        UsmFzAlarmAnalysisDto dto = usmFzAlarmAnalysisMapper.countAlarmsByStatus(startDate, endDate);
        // 处置完成率
//        double rate = dto.getTotalAlarms() == 0 ? 0 :
//                dto.getHandled() * 100.0 / dto.getTotalAlarms();
//
//        double roundedRate = NumberUtil.round(rate, 2).doubleValue();
//
//        response.setFalseAlarmRate(percentFormat(roundedRate));
        double completionRate = NumberUtil.round(dto.getHandled() * 100.0 / dto.getTotalAlarms(), 2).doubleValue();
        response.setCompletionRate(percentFormat(completionRate));

        UsmFzAlarmAnalysisDto falseAlarmCount = usmFzAlarmAnalysisMapper.countAlarmsByStatus(startDate, endDate);
        // 误报率
        double falseAlarmRate =  NumberUtil.round(falseAlarmCount.getFalseAlarm() * 100.0 / dto.getTotalAlarms(), 2).doubleValue();
        response.setFalseAlarmRate(percentFormat(falseAlarmRate));

        List<Double> handlingDurations = usmFzAlarmAnalysisMapper.getHandlingDurations(startDate, endDate);
        // 平均处置时长
        Double calculateAverage = calculateAverage(handlingDurations);
        response.setAvgHandlingDuration(DateUtils.formatHoursToDHM(calculateAverage));

        // 3. 计算环比数据(上周期)
        if (startDate != null && endDate != null){
            long periodMillis = DateUtil.between(startDate, endDate, DateUnit.MS);
            Date previousStart = DateUtil.offset(startDate, DateField.MILLISECOND, (int)-periodMillis);
            Date previousEnd = DateUtil.offset(endDate, DateField.MILLISECOND, (int)-periodMillis);

            UsmFzAlarmAnalysisDto prevStatusCounts = usmFzAlarmAnalysisMapper.countAlarmsByStatus(previousStart, previousEnd);
            List<Double> prevHandlingDurations = usmFzAlarmAnalysisMapper.getHandlingDurations(previousStart, previousEnd);

            // 计算上期指标
//        Double prevCompletionRate = calculateRate(prevStatusCounts.getHandled(), prevStatusCounts.getTotalAlarms());
//        Double prevFalseAlarmRate = calculateRate(prevStatusCounts.getFalseAlarm(), prevStatusCounts.getTotalAlarms());
            Double prevAvgDuration = calculateAverage(prevHandlingDurations);

            // 设置环比分析
            AlarmDisposalSituationResponse.MetricTrendAnalysis completionAnalysis =
                    createTrendAnalysis(dto.getHandled().doubleValue(), prevStatusCounts.getHandled().doubleValue());
            response.setCompletionAnalysis(percentFormat(completionAnalysis.getChangeRate()));
            response.setCompletionAnalysisTrend(completionAnalysis.getTrend());

            AlarmDisposalSituationResponse.MetricTrendAnalysis falseAlarmAnalysis =
                    createTrendAnalysis(dto.getFalseAlarm().doubleValue(), prevStatusCounts.getFalseAlarm().doubleValue());
            response.setFalseAlarmAnalysis(percentFormat(falseAlarmAnalysis.getChangeRate()));
            response.setFalseAlarmAnalysisTrend(falseAlarmAnalysis.getTrend());

            AlarmDisposalSituationResponse.MetricTrendAnalysis avgHandlingDurationAnalysis = createTrendAnalysis(calculateAverage, prevAvgDuration);
            response.setAvgHandlingDurationAnalysis(percentFormat(avgHandlingDurationAnalysis.getChangeRate()));
            response.setAvgHandlingDurationAnalysisTrend(avgHandlingDurationAnalysis.getTrend());
        }

        return response;
    }

    // 计算比率
    private Double calculateRate(Long numerator, Long denominator) {
        if (denominator == null || denominator == 0) {
            return 0d;
        }

        double roundedRate = NumberUtil.round(numerator * 100.0 / denominator, 2).doubleValue();

        return roundedRate;
    }

    // 计算平均值
    private Double calculateAverage(List<Double> values) {
        if (CollectionUtil.isEmpty(values)) {
            return null;
        }
        return values.stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
    }

    private AlarmDisposalSituationResponse.MetricTrendAnalysis createTrendAnalysis(Double current, Double previous) {
        AlarmDisposalSituationResponse.MetricTrendAnalysis analysis =
                new AlarmDisposalSituationResponse.MetricTrendAnalysis();
        if (previous != null && previous != 0) {
            double rate = (current - previous) * 100.0 / previous;
            analysis.setChangeRate(NumberUtil.round(rate, 2).doubleValue());
            analysis.setTrend(rate >= 0 ? "up" : "down");
        } else {
            analysis.setChangeRate(0d);
            analysis.setTrend("up");
        }

        return analysis;
    }

    @Override
    public AlarmTrendStatisticsResponse getAlarmTrendStatistics(UsmFzAlarmAnalysisForm request) {
        log.info("获取报警趋势分析统计信息, 开始天数: {}, 结束天数: {}", request.getStartDate(), request.getEndDate());
        AlarmTrendStatisticsResponse response = new AlarmTrendStatisticsResponse();

        // 获取报警趋势数据
        List<AlarmTrendStatisticsResponse.DailyAlarmStatistics> alarmTrendData =
                usmFzAlarmAnalysisMapper.getAlarmTrendData(request.getStartDate(), request.getEndDate());
        response.setStatistics(alarmTrendData);

        return response;
    }

    @Override
    public AlarmLevelStatisticsResponse getAlarmLevelCounts(UsmFzAlarmAnalysisForm request) {
        log.info("获取报警等级统计信息, 开始天数: {}, 结束天数: {}", request.getStartDate(), request.getEndDate());
        AlarmLevelStatisticsResponse response = new AlarmLevelStatisticsResponse();
        
        // 获取各等级报警数量
        List<AlarmLevelStatisticsResponse.AlarmLevelStatistics> alarmLevelStatistics =
                usmFzAlarmAnalysisMapper.getAlarmLevelCounts(request.getStartDate(), request.getEndDate());

        response.setStatistics(setPercent(alarmLevelStatistics));
        
        return response;
    }

    private List<AlarmLevelStatisticsResponse.AlarmLevelStatistics> setPercent(List<AlarmLevelStatisticsResponse.AlarmLevelStatistics> alarmLevelStatistics) {
        // 1. 参数校验
        if (alarmLevelStatistics == null || alarmLevelStatistics.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 计算总数
        Double totalCount = alarmLevelStatistics.stream()
                .mapToDouble(AlarmLevelStatisticsResponse.AlarmLevelStatistics::getTotalCount).sum();

        // 3. 处理总和为零的情况
        if (totalCount == 0) {
            alarmLevelStatistics.forEach(dto -> dto.setPercent("0%"));
            return alarmLevelStatistics;
        }

        // 4. 计算百分比
        Double accumulatedPercent = 0.0;
        DecimalFormat df = new DecimalFormat("#.##");
        df.setRoundingMode(RoundingMode.HALF_UP);

        for (int i = 0; i < alarmLevelStatistics.size(); i++) {
            AlarmLevelStatisticsResponse.AlarmLevelStatistics dto = alarmLevelStatistics.get(i);
            Integer count = dto.getTotalCount() != null ? dto.getTotalCount() : 0;

            // 计算当前项的百分比
            Double percent;
            if (i == alarmLevelStatistics.size() - 1) {
                // 最后一项用100减去累计值，确保总和为100%
                percent = 100 - accumulatedPercent;
            } else {
                percent = NumberUtil.round(count * 100.0 / totalCount, 2).doubleValue();
                accumulatedPercent += percent;
            }
            dto.setPercent(df.format(percent) + "%");
        }
        return alarmLevelStatistics;
    }


    @Override
    public Page<AlarmHighFrequencyAlarmDeviceResponse> getHighFrequencyAlarmDevice(
            UsmFzAlarmAnalysisForm request, Integer pageNum, Integer pageSize) {
        log.info("获取高发报警设备信息, 开始天数: {}, 结束天数: {}, 页码: {}, 每页大小: {}",
                request.getStartDate(), request.getEndDate(), pageNum, pageSize);

        Page<AlarmHighFrequencyAlarmDeviceResponse> page = new Page<>(pageNum, pageSize);

        return page.setRecords(usmFzAlarmAnalysisMapper.getHighFrequencyAlarmDevices(request.getStartDate(), request.getEndDate()));
    }

    @Override
    public Page<AlarmEnterPriseStatisticsResponse> getEnterpriseStatistics(UsmFzAlarmAnalysisForm request, Integer pageNum, Integer pageSize) {
        log.info("获取企业报警统计信息, 开始天数: {}, 结束天数: {}, 页码: {}, 每页大小: {}",
                request.getStartDate(), request.getEndDate(), pageNum, pageSize);

        Page<AlarmEnterPriseStatisticsResponse> page = new Page<>(pageNum, pageSize);

        return page.setRecords(usmFzAlarmAnalysisMapper.getEnterpriseStatistics(request.getStartDate(), request.getEndDate()));
    }
} 