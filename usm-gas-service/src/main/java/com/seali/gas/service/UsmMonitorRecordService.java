package com.seali.gas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.gas.entity.request.MonitorRecordRequest;
import com.seali.gas.entity.response.DeviceOfflineRecordResponse;
import com.seali.gas.pojo.UsmMonitorRecord;
import com.seali.gas.dto.UsmMonitorRecordDto;
import com.seali.gas.form.UsmMonitorRecordForm;
import com.seali.common.service.BaseService;

import java.util.List;

public interface UsmMonitorRecordService extends BaseService<UsmMonitorRecord, UsmMonitorRecordForm, UsmMonitorRecordDto, Long> {

    /**
     * 查询设备监测曲线
     *
     * @param request 查询条件，包含设备ID和时间范围
     * @return 设备监测曲线列表
     */
    List<UsmMonitorRecordDto> monitorCurve(MonitorRecordRequest request);

    /**
     * 查询设备监测曲线（分页）
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param request 查询条件，包含设备ID和时间范围
     * @return 设备监测曲线分页列表
     */
    Page<UsmMonitorRecordDto> monitorCurvePage(int pageNum, int pageSize, MonitorRecordRequest request);

    /**
     * 查询设备离线记录（分页）
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param request 查询条件，包含设备ID和时间范围
     * @return 设备离线记录分页列表
     */
    Page<DeviceOfflineRecordResponse> queryDeviceOfflineRecords(int pageNum, int pageSize, MonitorRecordRequest request);
}