package com.seali.gas.service.impl;

import com.github.pagehelper.PageInfo;
import com.hclight.common.result.PageResult;
import com.seali.common.result.Result;
import com.seali.common.service.impl.BaseServiceImpl;
import com.seali.gas.dao.UsmMonitorAlarmMapper;
import com.seali.gas.dto.UsmFzGasExpertDto;
import com.seali.gas.dto.UsmFzGasSchemeDto;
import com.seali.gas.dto.UsmMonitorAlarmDto;
import com.seali.gas.dto.UsmMonitorAlarmStatusDto;
import com.seali.gas.entity.request.AlarmConfirmRequest;
import com.seali.gas.entity.request.AlarmHandleRequest;
import com.seali.gas.entity.response.AlarmHandleListResponse;
import com.seali.gas.enums.info.MonitorDeviceEnum;
import com.seali.gas.form.UsmMonitorAlarmForm;
import com.seali.gas.form.UsmMonitorAlarmStatusForm;
import com.seali.gas.pojo.UsmMonitorAlarm;
import com.seali.gas.service.UsmMonitorAlarmService;
import com.seali.gas.service.UsmMonitorAlarmStatusService;
import com.seali.gas.util.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.github.pagehelper.page.PageMethod.startPage;

@Service
@RequiredArgsConstructor
public class UsmMonitorAlarmServiceImpl extends BaseServiceImpl<UsmMonitorAlarmMapper, UsmMonitorAlarm, UsmMonitorAlarmForm, UsmMonitorAlarmDto, Long> implements UsmMonitorAlarmService {

    private final UsmMonitorAlarmStatusService usmMonitorAlarmStatusService;

    private final UsmMonitorAlarmMapper usmMonitorAlarmMapper;

    @Override
    public Result<UsmMonitorAlarmDto> saveEntity(UsmMonitorAlarmForm form) {
        Result<UsmMonitorAlarmDto> usmMonitorAlarmDtoResult = super.saveEntity(form);
        if(200 != usmMonitorAlarmDtoResult.getCode()){
            return Result.error("新增失败");
        }
        UsmMonitorAlarmDto data = usmMonitorAlarmDtoResult.getData();

        UsmMonitorAlarmStatusForm usmMonitorAlarmStatusForm = new UsmMonitorAlarmStatusForm();
        usmMonitorAlarmStatusForm.setAlarmId(data.getId());
        usmMonitorAlarmStatusForm.setAlarmStatus(MonitorDeviceEnum.AlarmHandleProcessStatus.HAPPEN.getCode());
        usmMonitorAlarmStatusForm.setAlarmStatusName(MonitorDeviceEnum.AlarmHandleProcessStatus.HAPPEN.getName());
        usmMonitorAlarmStatusForm.setCreateTime(new Date());
        usmMonitorAlarmStatusForm.setUpdateTime(usmMonitorAlarmStatusForm.getCreateTime());
//        usmMonitorAlarmStatusForm.setDescription(ApiConstants.ALARM_HAPPENED);
        usmMonitorAlarmStatusService.saveEntity(usmMonitorAlarmStatusForm);
        return usmMonitorAlarmDtoResult;
    }

    @Override
    protected UsmMonitorAlarm formToEntity(UsmMonitorAlarmForm form) {
        UsmMonitorAlarm entity = new UsmMonitorAlarm();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmMonitorAlarmDto entityToDto(UsmMonitorAlarm entity) {
        UsmMonitorAlarmDto dto = new UsmMonitorAlarmDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public PageResult<UsmMonitorAlarmDto> findPage(UsmMonitorAlarmForm form, int page, int size) {
        //分页
        startPage(page,size);

        List<UsmMonitorAlarmDto> monitorAlarmDtos = usmMonitorAlarmMapper.findPage(form);

        PageInfo<UsmMonitorAlarmDto> infos= new PageInfo<>(monitorAlarmDtos);

        return new PageResult<>(infos.getTotal(),infos.getList());
    }

    @Override
    public UsmMonitorAlarmDto findById(Integer id) {
        UsmMonitorAlarmDto dto = usmMonitorAlarmMapper.findById(id);

        //计算持续时长
        Date alarmTime = dto.getAlarmTime();
        Date updateTime = dto.getUpdateTime();

        // 计算两个日期之间相差的小时数
        if (alarmTime != null && updateTime != null) {
            long diffInMillis = updateTime.getTime() - alarmTime.getTime();
            double hours = diffInMillis / (1000.0 * 60 * 60); // 转换为小时（含小数）
            dto.setDuration(DateUtils.formatHoursToDHM(hours));
        }

        return dto;
    }

    @Override
    public UsmMonitorAlarmDto findAlarmRecordByDeviceId(String deviceId) {
        UsmMonitorAlarmDto dto = usmMonitorAlarmMapper.findAlarmRecordByDeviceId(deviceId);
        return dto;
    }

    @Override
    public void alarmConfirm(AlarmConfirmRequest form) {
        UsmMonitorAlarm usmMonitorAlarm = new UsmMonitorAlarm();
        usmMonitorAlarm.setId(form.getAlarmId());

        UsmMonitorAlarmStatusForm usmMonitorAlarmStatusForm = new UsmMonitorAlarmStatusForm();
        usmMonitorAlarmStatusForm.setAlarmId(form.getAlarmId());
        usmMonitorAlarmStatusForm.setAlarmStatus(MonitorDeviceEnum.AlarmHandleProcessStatus.CONFIRM.getCode());
        usmMonitorAlarmStatusForm.setAlarmStatusName(MonitorDeviceEnum.AlarmHandleProcessStatus.CONFIRM.getName());
        usmMonitorAlarmStatusForm.setHandleUser(form.getHandleUser());

        if (form.getConfirmResult() == MonitorDeviceEnum.AlarmConfirmStatus.TRUE_ALARM.getCode()){
            usmMonitorAlarmStatusForm.setConfirmResult(MonitorDeviceEnum.AlarmConfirmStatus.TRUE_ALARM.getCode());
            usmMonitorAlarmStatusForm.setConfirmResultName(MonitorDeviceEnum.AlarmConfirmStatus.TRUE_ALARM.getName());

            // 报警状态改为待处置
            usmMonitorAlarm.setAlarmStatus(MonitorDeviceEnum.AlarmStatus.PENDING_HANDLE.getCode());
            usmMonitorAlarm.setAlarmStatusName(MonitorDeviceEnum.AlarmStatus.PENDING_HANDLE.getName());
        } else if (form.getConfirmResult() == MonitorDeviceEnum.AlarmConfirmStatus.FALSE_ALARM.getCode()){
            usmMonitorAlarmStatusForm.setConfirmResult(MonitorDeviceEnum.AlarmConfirmStatus.FALSE_ALARM.getCode());
            usmMonitorAlarmStatusForm.setConfirmResultName(MonitorDeviceEnum.AlarmConfirmStatus.FALSE_ALARM.getName());

            // 报警状态改为误报
            usmMonitorAlarm.setAlarmStatus(MonitorDeviceEnum.AlarmStatus.FALSE_ALARM.getCode());
            usmMonitorAlarm.setAlarmStatusName(MonitorDeviceEnum.AlarmStatus.FALSE_ALARM.getName());
        }
        usmMonitorAlarmStatusForm.setDescription(form.getDescription());
        usmMonitorAlarmStatusService.saveEntity(usmMonitorAlarmStatusForm);

        usmMonitorAlarm.setUpdateTime(new Date());
        usmMonitorAlarmMapper.updateById(usmMonitorAlarm);
    }

    @Override
    public void alarmHandle(AlarmHandleRequest form) {
        UsmMonitorAlarm usmMonitorAlarm = new UsmMonitorAlarm();
        usmMonitorAlarm.setId(form.getAlarmId());

        UsmMonitorAlarmStatusForm usmMonitorAlarmStatusForm = new UsmMonitorAlarmStatusForm();
        usmMonitorAlarmStatusForm.setAlarmId(form.getAlarmId());
        usmMonitorAlarmStatusForm.setAlarmStatus(MonitorDeviceEnum.AlarmHandleProcessStatus.HANDLE.getCode());
        usmMonitorAlarmStatusForm.setAlarmStatusName(MonitorDeviceEnum.AlarmHandleProcessStatus.HANDLE.getName());
        usmMonitorAlarmStatusForm.setUnit(form.getUnit());
        usmMonitorAlarmStatusForm.setPicUrls(form.getPicUrls());
        usmMonitorAlarmStatusForm.setRemarks(form.getRemarks());

        if (form.getHandleStatus() == MonitorDeviceEnum.AlarmHandleStatus.HANDLING.getCode()){
            usmMonitorAlarmStatusForm.setHandleStatus(MonitorDeviceEnum.AlarmHandleStatus.HANDLING.getCode());
            usmMonitorAlarmStatusForm.setHandleStatusName(MonitorDeviceEnum.AlarmHandleStatus.HANDLING.getName());

            // 报警状态改为处置中
            usmMonitorAlarm.setAlarmStatus(MonitorDeviceEnum.AlarmStatus.HANDLING.getCode());
            usmMonitorAlarm.setAlarmStatusName(MonitorDeviceEnum.AlarmStatus.HANDLING.getName());
        } else if (form.getHandleStatus() == MonitorDeviceEnum.AlarmHandleStatus.HANDLED.getCode()){
            usmMonitorAlarmStatusForm.setHandleStatus(MonitorDeviceEnum.AlarmHandleStatus.HANDLED.getCode());
            usmMonitorAlarmStatusForm.setHandleStatusName(MonitorDeviceEnum.AlarmHandleStatus.HANDLED.getName());

            // 报警状态改为处置完成
            usmMonitorAlarm.setAlarmStatus(MonitorDeviceEnum.AlarmStatus.HANDLED.getCode());
            usmMonitorAlarm.setAlarmStatusName(MonitorDeviceEnum.AlarmStatus.HANDLED.getName());
        }

        usmMonitorAlarmStatusForm.setDescription(form.getDescription());
        usmMonitorAlarmStatusForm.setHandleUser(form.getHandleUser());
        usmMonitorAlarmStatusService.saveEntity(usmMonitorAlarmStatusForm);

        usmMonitorAlarm.setUpdateTime(new Date());
        usmMonitorAlarmMapper.updateById(usmMonitorAlarm);
    }

    @Override
    public AlarmHandleListResponse alarmHandleList(Integer id) {
        AlarmHandleListResponse response = new AlarmHandleListResponse();
        //查询处置列表
        List<UsmMonitorAlarmStatusDto> usmMonitorAlarmStatusDtos = usmMonitorAlarmMapper.findAlarmHandleList(id);
        response.setUsmMonitorAlarmStatusDtos(usmMonitorAlarmStatusDtos);

        // 查询关联方案
        List<UsmFzGasSchemeDto> usmFzGasSchemeDtos = usmMonitorAlarmMapper.findConnectScheme(id);
        response.setUsmFzGasSchemeDtos(usmFzGasSchemeDtos);

        // 查询关联专家
        List<UsmFzGasExpertDto> usmFzGasExpertDtos = usmMonitorAlarmMapper.findConnectExpert(id);
        response.setUsmFzGasExpertDtos(usmFzGasExpertDtos);

        return response;
    }
}