package com.seali.gas.service.impl;

import com.seali.gas.pojo.UsmZyGasSurroundingWell;
import com.seali.gas.dto.UsmZyGasSurroundingWellDto;
import com.seali.gas.form.UsmZyGasSurroundingWellForm;
import com.seali.gas.dao.UsmZyGasSurroundingWellMapper;
import com.seali.gas.service.UsmZyGasSurroundingWellService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmZyGasSurroundingWellServiceImpl extends BaseServiceImpl<UsmZyGasSurroundingWellMapper, UsmZyGasSurroundingWell, UsmZyGasSurroundingWellForm, UsmZyGasSurroundingWellDto, Long> implements UsmZyGasSurroundingWellService {

    @Override
    protected UsmZyGasSurroundingWell formToEntity(UsmZyGasSurroundingWellForm form) {
        UsmZyGasSurroundingWell entity = new UsmZyGasSurroundingWell();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmZyGasSurroundingWellDto entityToDto(UsmZyGasSurroundingWell entity) {
        UsmZyGasSurroundingWellDto dto = new UsmZyGasSurroundingWellDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}