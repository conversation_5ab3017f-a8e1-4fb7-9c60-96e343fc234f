package com.seali.gas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.seali.gas.pojo.UsmSecurityReport;
import com.seali.gas.dto.UsmSecurityReportDto;
import com.seali.gas.form.UsmSecurityReportForm;
import com.seali.gas.dao.UsmSecurityReportMapper;
import com.seali.gas.service.UsmSecurityReportService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class UsmSecurityReportServiceImpl extends BaseServiceImpl<UsmSecurityReportMapper, UsmSecurityReport, UsmSecurityReportForm, UsmSecurityReportDto, Long> implements UsmSecurityReportService {

    @Override
    protected UsmSecurityReport formToEntity(UsmSecurityReportForm form) {
        UsmSecurityReport entity = new UsmSecurityReport();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmSecurityReportDto entityToDto(UsmSecurityReport entity) {
        UsmSecurityReportDto dto = new UsmSecurityReportDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public LambdaQueryWrapper<UsmSecurityReport> buildQueryWrapper(UsmSecurityReportForm form) {
        LambdaQueryWrapper<UsmSecurityReport> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(UsmSecurityReport::getIsDeleted, false); // 过滤未删除的数据

        // 根据报告名称查询
        queryWrapper.like(StringUtils.hasText(form.getReportName()), UsmSecurityReport::getReportName, form.getReportName());

        // 根据报告类型查询
        queryWrapper.eq(form.getReportType() != null, UsmSecurityReport::getReportType, form.getReportType());

        // 根据开始时间查询
        queryWrapper.ge(form.getStartTime() != null, UsmSecurityReport::getCreateTime, form.getStartTime());

        // 根据结束时间查询
        queryWrapper.le(form.getEndTime() != null, UsmSecurityReport::getCreateTime, form.getEndTime());


        return queryWrapper;
    }
}