package com.seali.gas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.gas.dto.UsmZyGasPipelineDto;
import com.seali.gas.entity.response.HomePageResponse;
import com.seali.gas.entity.response.risk.monitor.AlarmHighFrequencyAlarmDeviceResponse;

import java.util.List;

public interface HomePageService {
    UsmZyGasPipelineDto pipelineStatisticsByPressure();

    HomePageResponse alarmCount();

    HomePageResponse alarmStatisticsByLevel(Integer pageNum, Integer pageSize);

    Page<AlarmHighFrequencyAlarmDeviceResponse> alarmRank(Integer pageNum, Integer pageSize);

    HomePageResponse alarmStatisticsByTime(Integer dayIndex);
}
