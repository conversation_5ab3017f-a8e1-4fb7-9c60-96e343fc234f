package com.seali.gas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.seali.gas.pojo.UsmZyGasDanger;
import com.seali.gas.dto.UsmZyGasDangerDto;
import com.seali.gas.form.UsmZyGasDangerForm;
import com.seali.gas.dao.UsmZyGasDangerMapper;
import com.seali.gas.service.UsmZyGasDangerService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class UsmZyGasDangerServiceImpl extends BaseServiceImpl<UsmZyGasDangerMapper, UsmZyGasDanger, UsmZyGasDangerForm, UsmZyGasDangerDto, Long> implements UsmZyGasDangerService {

    @Override
    protected UsmZyGasDanger formToEntity(UsmZyGasDangerForm form) {
        UsmZyGasDanger entity = new UsmZyGasDanger();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmZyGasDangerDto entityToDto(UsmZyGasDanger entity) {
        UsmZyGasDangerDto dto = new UsmZyGasDangerDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public LambdaQueryWrapper<UsmZyGasDanger> buildQueryWrapper(UsmZyGasDangerForm form) {
        LambdaQueryWrapper<UsmZyGasDanger> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UsmZyGasDanger::getIsDeleted, false); // 过滤未删除的数据

        // 根据建筑类型查询
        queryWrapper.eq(form.getBuildingType() != null, UsmZyGasDanger::getBuildingType, form.getBuildingType());

        queryWrapper.eq(!StringUtils.isEmpty(form.getCity()), UsmZyGasDanger::getCity, form.getCity());
        queryWrapper.eq(!StringUtils.isEmpty(form.getCounty()), UsmZyGasDanger::getCounty, form.getCounty());
        queryWrapper.eq(!StringUtils.isEmpty(form.getTown()), UsmZyGasDanger::getTown, form.getTown());
        queryWrapper.eq(!StringUtils.isEmpty(form.getBuildingType()), UsmZyGasDanger::getBuildingType, form.getBuildingType());

        // 根据危险源编码或名称查询
        queryWrapper.and(StringUtils.hasText(form.getNameOrCode()), wrapper -> wrapper
                .like(UsmZyGasDanger::getDangerCode, form.getNameOrCode())
                .or()
                .like(UsmZyGasDanger::getDangerName, form.getNameOrCode()));

        return queryWrapper;
    }
}