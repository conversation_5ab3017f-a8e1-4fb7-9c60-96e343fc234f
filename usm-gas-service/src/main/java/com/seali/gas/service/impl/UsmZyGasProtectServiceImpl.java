package com.seali.gas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.seali.gas.form.UsmZyGasDangerForm;
import com.seali.gas.pojo.UsmZyGasDanger;
import com.seali.gas.pojo.UsmZyGasProtect;
import com.seali.gas.dto.UsmZyGasProtectDto;
import com.seali.gas.form.UsmZyGasProtectForm;
import com.seali.gas.dao.UsmZyGasProtectMapper;
import com.seali.gas.service.UsmZyGasProtectService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class UsmZyGasProtectServiceImpl extends BaseServiceImpl<UsmZyGasProtectMapper, UsmZyGasProtect, UsmZyGasProtectForm, UsmZyGasProtectDto, Long> implements UsmZyGasProtectService {

    @Override
    protected UsmZyGasProtect formToEntity(UsmZyGasProtectForm form) {
        UsmZyGasProtect entity = new UsmZyGasProtect();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmZyGasProtectDto entityToDto(UsmZyGasProtect entity) {
        UsmZyGasProtectDto dto = new UsmZyGasProtectDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public LambdaQueryWrapper<UsmZyGasProtect> buildQueryWrapper(UsmZyGasProtectForm form) {
        LambdaQueryWrapper<UsmZyGasProtect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UsmZyGasProtect::getIsDeleted, false); // 过滤未删除的数据

        // 根据建筑类型查询
        queryWrapper.eq(form.getBuildingType() != null, UsmZyGasProtect::getBuildingType, form.getBuildingType());

        queryWrapper.eq(!StringUtils.isEmpty(form.getCity()), UsmZyGasProtect::getCity, form.getCity());
        queryWrapper.eq(!StringUtils.isEmpty(form.getCounty()), UsmZyGasProtect::getCounty, form.getCounty());
        queryWrapper.eq(!StringUtils.isEmpty(form.getTown()), UsmZyGasProtect::getTown, form.getTown());
        queryWrapper.eq(!StringUtils.isEmpty(form.getBuildingType()), UsmZyGasProtect::getBuildingType, form.getBuildingType());

        // 根据危险源编码或名称查询
        queryWrapper.and(StringUtils.hasText(form.getNameOrCode()), wrapper -> wrapper
                .like(UsmZyGasProtect::getProtectCode, form.getNameOrCode())
                .or()
                .like(UsmZyGasProtect::getProtectName, form.getNameOrCode()));

        return queryWrapper;
    }
}