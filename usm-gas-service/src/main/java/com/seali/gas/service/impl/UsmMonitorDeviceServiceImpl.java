package com.seali.gas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.common.enums.ErrorCode;
import com.seali.common.exception.BusinessException;
import com.seali.common.id.Sid;
import com.seali.common.result.Result;
import com.seali.common.service.impl.BaseServiceImpl;
import com.seali.gas.dao.UsmMonitorDeviceMapper;
import com.seali.gas.dto.UsmMonitorDeviceDto;
import com.seali.gas.entity.request.EquipmentOperationMonitoringRequest;
import com.seali.gas.entity.response.DeviceRealTimeMonitoringResponse;
import com.seali.gas.entity.response.DeviceStatisticsResponse;
import com.seali.gas.entity.response.DeviceStatisticsByTypeResponse;
import com.seali.gas.form.UsmMonitorDeviceForm;
import com.seali.gas.pojo.UsmMonitorDevice;
import com.seali.gas.service.UsmMonitorDeviceService;
import com.seali.gas.util.GeometryUtils;
import lombok.extern.slf4j.Slf4j;
import org.postgis.Geometry;
import org.postgis.Point;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UsmMonitorDeviceServiceImpl extends BaseServiceImpl<UsmMonitorDeviceMapper, UsmMonitorDevice, UsmMonitorDeviceForm, UsmMonitorDeviceDto, String> implements UsmMonitorDeviceService {

    @Override
    public Result<UsmMonitorDeviceDto> saveEntity(UsmMonitorDeviceForm form) {
        form.setId(Sid.nextShort());
        return super.saveEntity(form);
    }

    @Override
    public Result<Boolean> saveBatchEntity(List<UsmMonitorDeviceForm> formList) {
        formList.forEach(form -> form.setId(Sid.nextShort()));
        return super.saveBatchEntity(formList);
    }

    @Override
    public Result<Boolean> updateEntityById(UsmMonitorDeviceForm form) {
        LambdaUpdateWrapper<UsmMonitorDevice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UsmMonitorDevice::getDataId, form.getId());

        UsmMonitorDevice entity = new UsmMonitorDevice();
        BeanUtils.copyProperties(form, entity);

        int update = baseMapper.update(entity, updateWrapper);
        return update > 0 ? Result.success(true) : Result.error("更新失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateBatchEntityById(List<UsmMonitorDeviceForm> formList) {
        for (UsmMonitorDeviceForm form : formList) {
            updateEntityById(form);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> removeEntityById(String s) {
        int delete = baseMapper.deleteByDataId(s);
        return delete > 0 ? Result.success(true) : Result.error("删除失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> removeBatchEntityByIds(List<String> strings) {
        for (String id : strings) {
            removeEntityById(id);
        }
        return Result.success(true);
    }

    @Override
    public Result<UsmMonitorDeviceDto> getEntityById(String s) {
        LambdaQueryWrapper<UsmMonitorDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UsmMonitorDevice::getDataId, s);
        UsmMonitorDevice usmMonitorDevice = baseMapper.selectOne(wrapper);
        UsmMonitorDeviceDto usmMonitorDeviceDto = entityToDto(usmMonitorDevice);
        return Result.success(usmMonitorDeviceDto);
    }

    @Override
    protected UsmMonitorDevice formToEntity(UsmMonitorDeviceForm form) {
        UsmMonitorDevice entity = new UsmMonitorDevice();
        BeanUtils.copyProperties(form, entity);
        entity.setId(null);

        if(!StringUtils.isEmpty(form.getId())){
            entity.setDataId(form.getId());
        }

        try {
            if (!StringUtils.isEmpty(form.getGeomText())) {
                Point geom = new Point(form.getGeomText());
                entity.setLongitude(String.valueOf(geom.getX()));
                entity.setLatitude(String.valueOf(geom.getY()));
                entity.setGeom(geom);
            }else if (!StringUtils.isEmpty(form.getLongitude()) && !StringUtils.isEmpty(form.getLatitude())) {
                entity.setGeom(new Point(Double.parseDouble(form.getLongitude()), Double.parseDouble(form.getLatitude())));
            }
        } catch (SQLException e) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "坐标信息有误，及：geomText, longitude, latitude");
        }
        return entity;
    }

    @Override
    protected UsmMonitorDeviceDto entityToDto(UsmMonitorDevice entity) {
        UsmMonitorDeviceDto dto = new UsmMonitorDeviceDto();
        BeanUtils.copyProperties(entity, dto);

        dto.setId(entity.getDataId());
        // 处理几何数据
        Geometry geom = entity.getGeom();
        if (geom != null) {
            dto.setGeomText(GeometryUtils.postgisGeometryToWkt(geom));
        } else if (entity.getLongitude() != null && entity.getLatitude() != null
                && !StringUtils.isEmpty(entity.getLongitude()) && !StringUtils.isEmpty(entity.getLatitude())) {
            // 如果没有 geom 字段但有经纬度，则构造纯粹的 WKT 格式文本
            dto.setGeomText(String.format("POINT(%s %s)", entity.getLongitude(), entity.getLatitude()));
        }

        return dto;
    }

    @Override
    public List<DeviceRealTimeMonitoringResponse> queryDeviceRealTimeMonitoring(EquipmentOperationMonitoringRequest request) {
        List<DeviceRealTimeMonitoringResponse> result = baseMapper.queryDeviceRealTimeMonitoring(null, request);
        processDeviceStatusDescriptions(result);
        return result;
    }

    @Override
    public Page<DeviceRealTimeMonitoringResponse> queryDeviceRealTimeMonitoringPage(int pageNum, int pageSize, EquipmentOperationMonitoringRequest request) {
        log.info("分页查询设备实时监测数据, 页码: {}, 每页大小: {}, 查询条件: {}", pageNum, pageSize, request);

        // 创建分页对象
        Page<DeviceRealTimeMonitoringResponse> page = new Page<>(pageNum, pageSize);

        // 使用原有方法进行分页查询
        List<DeviceRealTimeMonitoringResponse> result = baseMapper.queryDeviceRealTimeMonitoring(page, request);

        // 处理设备状态描述
        processDeviceStatusDescriptions(result);

        return page.setRecords(result);
    }

    /**
     * 处理设备状态描述
     *
     * @param devices 设备列表
     */
    private void processDeviceStatusDescriptions(List<DeviceRealTimeMonitoringResponse> devices) {
        if (devices == null || devices.isEmpty()) {
            return;
        }

        for (DeviceRealTimeMonitoringResponse device : devices) {
            // 设置工作状态描述
            if (device.getWorkStatus() != null) {
                switch (device.getWorkStatus()) {
                    case 0:
                        device.setWorkStatusDesc("正常");
                        break;
                    case 1:
                        device.setWorkStatusDesc("超量程");
                        break;
                    case 2:
                        device.setWorkStatusDesc("超工作温度");
                        break;
                    case 3:
                        device.setWorkStatusDesc("甲烷传感器光路污染");
                        break;
                    case 4:
                        device.setWorkStatusDesc("甲烷传感器光强饱和");
                        break;
                    case 5:
                        device.setWorkStatusDesc("液位报警");
                        break;
                    case 6:
                        device.setWorkStatusDesc("甲烷冷凝结露");
                        break;
                    case 7:
                        device.setWorkStatusDesc("甲烷传感器温控失锁");
                        break;
                    default:
                        device.setWorkStatusDesc("未知状态");
                }
            }

            // 设置在线状态描述
            if (device.getOnlineStatus() != null) {
                if (device.getOnlineStatus() == 1) {
                    device.setOnlineStatusDesc("在线");
                } else {
                    device.setOnlineStatusDesc("离线");
                }
            }
        }
    }

    @Override
    public boolean updateGeomByLongLat(String id, String longitude, String latitude) {
        log.info("根据经纬度更新设备空间坐标, 设备ID: {}, 经度: {}, 纬度: {}", id, longitude, latitude);
        if (!StringUtils.hasText(id) || !StringUtils.hasText(longitude) || !StringUtils.hasText(latitude)) {
            log.error("参数不完整，无法更新空间坐标");
            return false;
        }

        try {
            int result = baseMapper.updateGeomByLongLat(id, longitude, latitude);
            return result > 0;
        } catch (Exception e) {
            log.error("更新设备空间坐标失败", e);
            return false;
        }
    }

    @Override
    public boolean updateGeomByWkt(String id, String wkt) {
        log.info("根据WKT更新设备空间坐标, 设备ID: {}, WKT: {}", id, wkt);
        if (!StringUtils.hasText(id) || !StringUtils.hasText(wkt)) {
            log.error("参数不完整，无法更新空间坐标");
            return false;
        }

        try {
            int result = baseMapper.updateGeomByWkt(id, wkt);
            return result > 0;
        } catch (Exception e) {
            log.error("更新设备空间坐标失败", e);
            return false;
        }
    }

    @Override
    public List<UsmMonitorDeviceDto> findDevicesWithinDistance(String longitude, String latitude, double distance) {
        log.info("根据空间范围查询设备, 中心点: [{}, {}], 距离: {}米", longitude, latitude, distance);
        if (!StringUtils.hasText(longitude) || !StringUtils.hasText(latitude) || distance <= 0) {
            log.error("参数不完整，无法查询");
            return new ArrayList<>();
        }

        try {
            List<UsmMonitorDevice> devices = baseMapper.findDevicesWithinDistance(longitude, latitude, distance);
            return devices.stream().map(this::entityToDto).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询设备失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<UsmMonitorDeviceDto> findDevicesWithinPolygon(String wktPolygon) {
        log.info("根据多边形范围查询设备, 多边形: {}", wktPolygon);
        if (!StringUtils.hasText(wktPolygon)) {
            log.error("多边形参数不能为空");
            return new ArrayList<>();
        }

        try {
            List<UsmMonitorDevice> devices = baseMapper.findDevicesWithinPolygon(wktPolygon);
            return devices.stream().map(this::entityToDto).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询设备失败", e);
            return new ArrayList<>();
        }
    }


    @Override
    public DeviceStatisticsResponse queryDeviceStatistics() {
        log.info("查询设备统计数据");
        try {
            // 直接调用Mapper方法获取统计数据对象
            DeviceStatisticsResponse result = baseMapper.queryDeviceStatistics();
            log.info("查询设备统计数据成功: {}", result);
            return result;
        } catch (Exception e) {
            log.error("查询设备统计数据失败", e);
            // 发生异常时返回空结果
            DeviceStatisticsResponse emptyResult = new DeviceStatisticsResponse();
            emptyResult.setTotalDeviceCount(0);
            emptyResult.setNormalDeviceCount(0);
            emptyResult.setOfflineDeviceCount(0);
            emptyResult.setAlarmDeviceCount(0);
            emptyResult.setOnlineRate(0.0);
            return emptyResult;
        }
    }

    @Override
    public List<DeviceStatisticsByTypeResponse> queryDeviceStatisticsByType() {
        log.info("按设备类型查询设备统计数据");
        try {
            // 直接调用Mapper方法获取按设备类型分组的统计数据列表
            List<DeviceStatisticsByTypeResponse> result = baseMapper.queryDeviceStatisticsByType();
            log.info("按设备类型查询设备统计数据成功, 共返回 {} 条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("按设备类型查询设备统计数据失败", e);
            // 发生异常时返回空列表
            return new ArrayList<>();
        }
    }
}