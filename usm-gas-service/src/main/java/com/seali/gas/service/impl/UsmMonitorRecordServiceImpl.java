package com.seali.gas.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.gas.entity.request.MonitorRecordRequest;
import com.seali.gas.entity.response.DeviceOfflineRecordResponse;
import com.seali.gas.pojo.UsmMonitorRecord;
import com.seali.gas.dto.UsmMonitorRecordDto;
import com.seali.gas.form.UsmMonitorRecordForm;
import com.seali.gas.dao.UsmMonitorRecordMapper;
import com.seali.gas.service.UsmMonitorRecordService;
import com.seali.common.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class UsmMonitorRecordServiceImpl extends BaseServiceImpl<UsmMonitorRecordMapper, UsmMonitorRecord, UsmMonitorRecordForm, UsmMonitorRecordDto, Long> implements UsmMonitorRecordService {

    private final UsmMonitorRecordMapper usmMonitorRecordMapper;

    @Override
    protected UsmMonitorRecord formToEntity(UsmMonitorRecordForm form) {
        UsmMonitorRecord entity = new UsmMonitorRecord();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmMonitorRecordDto entityToDto(UsmMonitorRecord entity) {
        UsmMonitorRecordDto dto = new UsmMonitorRecordDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public List<UsmMonitorRecordDto> monitorCurve(MonitorRecordRequest request) {
        List<UsmMonitorRecordDto> dto = usmMonitorRecordMapper.monitorCurve(request);
        return dto;
    }

    @Override
    public Page<UsmMonitorRecordDto> monitorCurvePage(int pageNum, int pageSize, MonitorRecordRequest request) {
        log.info("监测历史记录-在线(分页), 设备ID: {}, 页码: {}, 每页大小: {}, 开始时间: {}, 结束时间: {}",
                request.getDeviceId(), pageNum, pageSize, request.getStartTime(), request.getEndTime());

        try {
            // 创建分页对象
            Page<UsmMonitorRecordDto> page = new Page<>(pageNum, pageSize);

            // 执行分页查询
            Page<UsmMonitorRecordDto> resultPage = usmMonitorRecordMapper.monitorCurvePage(page, request);

            log.info("查询设备监测曲线成功, 总记录数: {}, 当前页记录数: {}",
                    resultPage.getTotal(), resultPage.getRecords().size());
            return resultPage;
        } catch (Exception e) {
            log.error("查询设备监测曲线失败", e);
            throw e;
        }
    }

    @Override
    public Page<DeviceOfflineRecordResponse> queryDeviceOfflineRecords(int pageNum, int pageSize, MonitorRecordRequest request) {
        log.info("查询设备离线记录(分页), 设备ID: {}, 页码: {}, 每页大小: {}, 开始时间: {}, 结束时间: {}",
                request.getDeviceId(), pageNum, pageSize, request.getStartTime(), request.getEndTime());

        try {
            // 检查设备状态
            checkDeviceStatus(request.getDeviceId());

            // 创建分页对象
            Page<DeviceOfflineRecordResponse> page = new Page<>(pageNum, pageSize);

            // 执行分页查询
            Page<DeviceOfflineRecordResponse> resultPage = usmMonitorRecordMapper.queryDeviceOfflineRecords(page, request);

            log.info("查询设备离线记录成功, 总记录数: {}, 当前页记录数: {}",
                    resultPage.getTotal(), resultPage.getRecords().size());
            return resultPage;
        } catch (Exception e) {
            log.error("查询设备离线记录失败", e);
            throw e;
        }
    }

    public void checkDeviceStatus(String deviceId) {
        List<UsmMonitorRecord> usmMonitorRecords = usmMonitorRecordMapper.queryDeviceRecords(deviceId);
        if (usmMonitorRecords.isEmpty()) {
            return;
        }
        UsmMonitorRecord usmMonitorRecord = usmMonitorRecords.get(0);
        usmMonitorRecord.setOfflineTime(usmMonitorRecord.getMonitorTime());

        List<UsmMonitorRecord> updateRecords = new ArrayList<>();
        List<Long> removeIds = new ArrayList<>();
        for (int i = 1; i < usmMonitorRecords.size(); i++) {
            UsmMonitorRecord pr = usmMonitorRecords.get(i - 1);
            UsmMonitorRecord cr = usmMonitorRecords.get(i);
            if(pr.getMonitorStatus() == 9002 && cr.getMonitorStatus() == 9001){
                usmMonitorRecord.setRecoveryTime(cr.getMonitorTime());
                usmMonitorRecord.setOfflineDuration(String.valueOf((usmMonitorRecord.getRecoveryTime().getTime() - usmMonitorRecord.getOfflineTime().getTime()) / 1000 / 60));
                updateRecords.add(usmMonitorRecord);
            }else if(pr.getMonitorStatus() == 9002 && cr.getMonitorStatus() == 9002){
                removeIds.add(cr.getId());
            }else if (pr.getMonitorStatus() == 9001 && cr.getMonitorStatus() == 9002) {
                usmMonitorRecord = cr;
                usmMonitorRecord.setOfflineTime(cr.getMonitorTime());
            }
        }
        if(usmMonitorRecord.getRecoveryTime() == null){
            updateRecords.add(usmMonitorRecord);
        }
        super.updateBatchById(updateRecords);
        super.removeByIds(removeIds);
    }
}