package com.seali.gas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.seali.gas.pojo.UsmMonitorAlarmStatus;
import com.seali.gas.dto.UsmMonitorAlarmStatusDto;
import com.seali.gas.form.UsmMonitorAlarmStatusForm;
import com.seali.gas.dao.UsmMonitorAlarmStatusMapper;
import com.seali.gas.service.UsmMonitorAlarmStatusService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmMonitorAlarmStatusServiceImpl extends BaseServiceImpl<UsmMonitorAlarmStatusMapper, UsmMonitorAlarmStatus, UsmMonitorAlarmStatusForm, UsmMonitorAlarmStatusDto, Long> implements UsmMonitorAlarmStatusService {

    @Override
    protected UsmMonitorAlarmStatus formToEntity(UsmMonitorAlarmStatusForm form) {
        UsmMonitorAlarmStatus entity = new UsmMonitorAlarmStatus();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmMonitorAlarmStatusDto entityToDto(UsmMonitorAlarmStatus entity) {
        UsmMonitorAlarmStatusDto dto = new UsmMonitorAlarmStatusDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public LambdaQueryWrapper<UsmMonitorAlarmStatus> buildQueryWrapper(UsmMonitorAlarmStatusForm form) {
        LambdaQueryWrapper<UsmMonitorAlarmStatus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UsmMonitorAlarmStatus::getIsDeleted, false); // 过滤未删除的数据

        // 根据报警id查询
        queryWrapper.eq(form.getAlarmId() != null, UsmMonitorAlarmStatus::getAlarmId, form.getAlarmId());

        // 根据时间排序
        queryWrapper.orderByAsc(UsmMonitorAlarmStatus::getUpdateTime);
        return queryWrapper;
    }
}