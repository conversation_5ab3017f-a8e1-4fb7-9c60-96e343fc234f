package com.seali.gas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.seali.common.enums.ErrorCode;
import com.seali.common.exception.BusinessException;
import com.seali.common.result.Result;
import com.seali.common.service.impl.BaseServiceImpl;
import com.seali.common.util.TimeUtil;
import com.seali.gas.constant.ApiConstants;
import com.seali.gas.dao.UsmAlarmThresholdMapper;
import com.seali.gas.dao.UsmMonitorDeviceMapper;
import com.seali.gas.dto.UsmAlarmThresholdDto;
import com.seali.gas.entity.response.DeviceRealTimeMonitoringResponse;
import com.seali.gas.enums.EnumUtil;
import com.seali.gas.enums.info.MonitorDeviceEnum;
import com.seali.gas.form.UsmAlarmThresholdDeviceForm;
import com.seali.gas.form.UsmAlarmThresholdForm;
import com.seali.gas.form.UsmMonitorAlarmForm;
import com.seali.gas.pojo.UsmAlarmThreshold;
import com.seali.gas.pojo.UsmAlarmThresholdDevice;
import com.seali.gas.pojo.UsmMonitorAlarm;
import com.seali.gas.service.UsmAlarmThresholdDeviceService;
import com.seali.gas.service.UsmAlarmThresholdService;
import com.seali.gas.service.UsmMonitorAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UsmAlarmThresholdServiceImpl extends BaseServiceImpl<UsmAlarmThresholdMapper, UsmAlarmThreshold, UsmAlarmThresholdForm, UsmAlarmThresholdDto, Long> implements UsmAlarmThresholdService {

    private final UsmAlarmThresholdDeviceService usmAlarmThresholdDeviceService;

    private final UsmMonitorDeviceMapper usmAlarmThresholdMapper;

    private final UsmMonitorAlarmService usmMonitorAlarmService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateEntityById(UsmAlarmThresholdForm form) {
        Long id = form.getId();
        if(id == null){
            throw new BusinessException(ErrorCode.BAD_REQUEST.getCode(), "id不能为空");
        }
        // form.getDeviceIds() 转 List<INTEGER>
        List<String> deviceIds = form.getDeviceIds();

        // 删除原来的关联设备
        List<Long> longList = deviceIds.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        usmAlarmThresholdDeviceService.removeBatchEntityByIds(longList);
        // 更新报警阈值
        Result<Boolean> result = super.updateEntityById(form);
        // 新增关联设备
        Result<Boolean> booleanResult = saveDevice(form.getDeviceIds(), id);
        if(200 != booleanResult.getCode()){
            return Result.error(booleanResult.getMessage());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<UsmAlarmThresholdDto> saveEntity(UsmAlarmThresholdForm form) {
        Result<UsmAlarmThresholdDto> result = super.saveEntity(form);
        if(200 != result.getCode()){
            return Result.error("新增失败");
        }
        UsmAlarmThresholdDto data = result.getData();
        Long id = data.getId();

        Result<Boolean> booleanResult = saveDevice(form.getDeviceIds(), id);
        if(200 != booleanResult.getCode()){
            return Result.error(booleanResult.getMessage());
        }
        return result;
    }

//-------------------------------TASK---------------------------------------------

    /**
     * 查询设备是否到达报警阈值
     */
    public void queryDeviceAlarmThreshold() {
        // 1. 查询所有报警阈值
        LambdaQueryWrapper<UsmAlarmThreshold> wrapper = new LambdaQueryWrapper<UsmAlarmThreshold>();
        wrapper.eq(UsmAlarmThreshold::getIsDeleted, false);
        List<UsmAlarmThreshold> alarmThresholds = list(wrapper);
        // 2. 查询所有设备
        alarmThresholds.forEach(alarmThreshold -> {
            BigDecimal thresholdLevel1Min = alarmThreshold.getThresholdLevel1Min();
            BigDecimal thresholdLevel1Max = alarmThreshold.getThresholdLevel1Max();
            BigDecimal thresholdLevel2Min = alarmThreshold.getThresholdLevel2Min();
            BigDecimal thresholdLevel2Max = alarmThreshold.getThresholdLevel2Max();
            BigDecimal thresholdLevel3Min = alarmThreshold.getThresholdLevel3Min();
            BigDecimal thresholdLevel3Max = alarmThreshold.getThresholdLevel3Max();
            // 2.1 查询设备
            List<UsmAlarmThresholdDevice> devices = usmAlarmThresholdDeviceService.lambdaQuery()
                    .eq(UsmAlarmThresholdDevice::getThresholdId, alarmThreshold.getId())
                    .list();
            devices.forEach(device -> {
                // 甲烷浓度 TODO 后续改成获取多条（及多条 DeviceRealTimeMonitoringResponse），取第一条数据使用；然后将查询的所有监测数据移到历史表
                DeviceRealTimeMonitoringResponse response = usmAlarmThresholdMapper.queryDeviceRealTimeMonitoringById(device.getDeviceId());
                if(response == null || response.getValue() == null){
                    log.info("设备ID: " + device.getDeviceId() + ", 未查询到设备数据");
                    return;
                }

                //查询设备最近一期的报警数据
                LambdaQueryWrapper<UsmMonitorAlarm> alarmWrapper = new LambdaQueryWrapper<UsmMonitorAlarm>();
                alarmWrapper.eq(UsmMonitorAlarm::getDeviceId, device.getDeviceId());
//                alarmWrapper.eq(UsmMonitorAlarm::getIsDeleted, false);
                alarmWrapper.orderByDesc(UsmMonitorAlarm::getCreateTime);
                alarmWrapper.last("limit 1");
//                alarmWrapper.select(UsmMonitorAlarm::getAlarmLevel);

                UsmMonitorAlarm oldAlarm = usmMonitorAlarmService.getOne(alarmWrapper);

                //判断是否有报警数据，且报警时用的数据与现在的查询的response的报警数据id是否相同，相同则结束，及没有新的报警数据来
                if(oldAlarm != null && oldAlarm.getMonitorDataId().equals(response.getMonitorDataId())){
                    log.info("设备ID: {}, 之前有报警信息，且报警时用的监测数据ID：{}与当前查询的最新一条监测数据id相同，结束", device.getDeviceId(), oldAlarm.getMonitorDataId());
                    return;
                }

                BigDecimal bigDecimal = new BigDecimal(response.getValue());
                // 比较设备数据是否到达报警阈值
                int alarmLevel = 0; // 0表示未报警，1-3表示报警级别
                String alarmMessage = null;
                //比较设备数据是否到达报警阈值
                // 判断是否达到一级报警阈值
                if (thresholdLevel1Min != null && thresholdLevel1Max != null &&
                    bigDecimal.compareTo(thresholdLevel1Min) >= 0 && bigDecimal.compareTo(thresholdLevel1Max) <= 0) {
                    alarmLevel = MonitorDeviceEnum.AlarmLevel.LEVEL_ONE.getCode();
                    alarmMessage = "甲烷浓度达到一级报警阈值：" + bigDecimal + "，阈值范围：[" + thresholdLevel1Min + ", " + thresholdLevel1Max + "]";
                }
                // 判断是否达到二级报警阈值
                else if (thresholdLevel2Min != null && thresholdLevel2Max != null &&
                         bigDecimal.compareTo(thresholdLevel2Min) >= 0 && bigDecimal.compareTo(thresholdLevel2Max) <= 0) {
                    alarmLevel = MonitorDeviceEnum.AlarmLevel.LEVEL_TWO.getCode();
                    alarmMessage = "甲烷浓度达到二级报警阈值：" + bigDecimal + "，阈值范围：[" + thresholdLevel2Min + ", " + thresholdLevel2Max + "]";
                }
                // 判断是否达到三级报警阈值
                else if (thresholdLevel3Min != null && thresholdLevel3Max != null &&
                         bigDecimal.compareTo(thresholdLevel3Min) >= 0 && bigDecimal.compareTo(thresholdLevel3Max) <= 0) {
                    alarmLevel = MonitorDeviceEnum.AlarmLevel.LEVEL_THREE.getCode();
                    alarmMessage = "甲烷浓度达到三级报警阈值：" + bigDecimal + "，阈值范围：[" + thresholdLevel3Min + ", " + thresholdLevel3Max + "]";
                }
                // 判断是否超过最高阈值
                else if (thresholdLevel3Max != null && bigDecimal.compareTo(thresholdLevel3Max) > 0) {
                    alarmLevel = MonitorDeviceEnum.AlarmLevel.LEVEL_THREE.getCode(); // 超过最高阈值，按三级报警处理
                    alarmMessage = "甲烷浓度超过最高报警阈值：" + bigDecimal + "，最高阈值：" + thresholdLevel3Max;
                }
                if(alarmLevel <= 0){
                    return;
                }
                log.info("设备ID: " + device.getDeviceId() + ", " + alarmMessage);

                UsmMonitorAlarmForm alarm = new UsmMonitorAlarmForm();

                alarm.setAlarmValue(response.getValue());
                alarm.setAlarmLevel(alarmLevel);
                alarm.setAlarmLevelName(EnumUtil.getNameByCode(MonitorDeviceEnum.AlarmLevel.class, alarmLevel));

                //判断是否报警过期: 没有删除，非误报或已处置，及之前的报警是否有效，有效则更新报警等级、报警值等相关信息
                if (oldAlarm != null && !oldAlarm.getIsDeleted() &&
                        (oldAlarm.getAlarmStatus() != MonitorDeviceEnum.AlarmStatus.FALSE_ALARM.getCode() ||
                                oldAlarm.getAlarmStatus() != MonitorDeviceEnum.AlarmStatus.HANDLED.getCode())) {
                    alarm.setId(oldAlarm.getId());
                    usmMonitorAlarmService.updateEntityById(alarm);
                }else {
                    // 如果触发报警，记录报警信息或发送通知
                    alarm.setDeviceId(device.getDeviceId());
                    alarm.setAlarmSource(response.getMonitorSource());
                    alarm.setMonitorObjectId(response.getMonitorObjectId());
                    alarm.setMonitorObjectName(response.getMonitorObjectName());
                    alarm.setAlarmLocation(response.getAddress());

                    alarm.setAlarmStatus(MonitorDeviceEnum.AlarmStatus.PENDING_CONFIRM.getCode()); // 待确认
                    alarm.setAlarmStatusName(MonitorDeviceEnum.AlarmStatus.PENDING_CONFIRM.getName());

                    alarm.setAlarmTime(new Timestamp(System.currentTimeMillis()));
                    alarm.setAlarmCode(ApiConstants.ALARM_PREFIX + TimeUtil.format(LocalDateTime.now(), TimeUtil.PATTERN_DATETIME_COMPACT));
                    usmMonitorAlarmService.saveEntity(alarm);
                }

            });

        });
    }

//--------------------------------------------------------------------------------





    private Result<Boolean> saveDevice(List<String> deviceIds, Long id) {
        if(deviceIds == null || deviceIds.isEmpty()){
            throw new BusinessException(ErrorCode.BAD_REQUEST.getCode(), "设备Ids不能为空");
        }
        List<UsmAlarmThresholdDeviceForm> deviceForms = new ArrayList<>();
        deviceIds.forEach(deviceId -> {
            UsmAlarmThresholdDeviceForm deviceForm = new UsmAlarmThresholdDeviceForm();
            deviceForm.setThresholdId(id);
            deviceForm.setDeviceId(deviceId);
            deviceForms.add(deviceForm);
        });
        return usmAlarmThresholdDeviceService.saveBatchEntity(deviceForms);
    }

    @Override
    protected UsmAlarmThreshold formToEntity(UsmAlarmThresholdForm form) {
        UsmAlarmThreshold entity = new UsmAlarmThreshold();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmAlarmThresholdDto entityToDto(UsmAlarmThreshold entity) {
        UsmAlarmThresholdDto dto = new UsmAlarmThresholdDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}