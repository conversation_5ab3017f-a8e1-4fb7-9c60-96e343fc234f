package com.seali.gas.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seali.gas.dto.UsmMonitorRecordDto;
import com.seali.gas.entity.request.MonitorRecordRequest;
import com.seali.gas.entity.response.DeviceOfflineRecordResponse;
import com.seali.gas.pojo.UsmMonitorRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 设备监测记录表 Dao 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-19
 */
public interface UsmMonitorRecordMapper extends BaseMapper<UsmMonitorRecord> {

    /**
     * 查询设备监测曲线
     *
     * @param request 查询条件，包含设备ID和时间范围
     * @return 设备监测曲线列表
     */
    List<UsmMonitorRecordDto> monitorCurve(MonitorRecordRequest request);

    /**
     * 监测历史记录-在线（分页）
     *
     * @param page 分页参数
     * @param request 查询条件，包含设备ID和时间范围
     * @return 设备监测曲线分页列表
     */
    Page<UsmMonitorRecordDto> monitorCurvePage(Page<UsmMonitorRecordDto> page, @Param("request") MonitorRecordRequest request);

    /**
     * 查询设备离线记录（分页）
     *
     * @param page 分页参数
     * @param request 查询条件，包含设备ID和时间范围
     * @return 设备离线记录分页列表
     */
    Page<DeviceOfflineRecordResponse> queryDeviceOfflineRecords(Page<DeviceOfflineRecordResponse> page, @Param("request") MonitorRecordRequest request);

    List<UsmMonitorRecord> queryDeviceRecords(String deviceId);
}
