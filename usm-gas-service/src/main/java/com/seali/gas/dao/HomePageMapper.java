package com.seali.gas.dao;

import com.seali.gas.dto.UsmZyGasPipelineDto;
import com.seali.gas.entity.response.HomePageResponse;
import com.seali.gas.entity.response.risk.monitor.AlarmHighFrequencyAlarmDeviceResponse;
import com.seali.gas.entity.response.risk.monitor.AlarmStatisticsResponse;
import com.seali.gas.entity.response.risk.monitor.AlarmTrendStatisticsResponse;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 首页mapper接口
 */
@Mapper
public interface HomePageMapper {

    /**
     * 根据压力级别统计管线长度
     * @return
     */
    UsmZyGasPipelineDto pipelineStatisticsByPressure();

    /**
     * 查询管网维修次数
     * @return
     */
    Integer getRepairCount();

    /**
     * 今日、本月报警数量统计
     * @return
     */
    HomePageResponse alarmCount();

    /**
     * 根据报警等级统计待处置报警数量
     * @return
     */
    HomePageResponse alarmStatisticsByLevel();

    /**
     * 查询待处置报警列表
     * @return
     */
    List<AlarmStatisticsResponse.AlarmInfo> getAlarmList();

    /**
     * 获取近30日燃气泄漏报警排名
     * @return
     */
    List<AlarmHighFrequencyAlarmDeviceResponse> getAlarmRank();


    /**
     * 报警统计
     * @return
     */
    HomePageResponse getAlarmStatistics();

    /**
     * 查询报警曲线数据
     * @param dayIndex
     * @return
     */
    List<AlarmTrendStatisticsResponse.DailyAlarmStatistics> getAlarmTrendData(Integer dayIndex);
}
