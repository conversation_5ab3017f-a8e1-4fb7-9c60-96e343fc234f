package com.seali.gas.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.seali.gas.entity.request.EquipmentOperationMonitoringRequest;
import com.seali.gas.entity.response.DeviceRealTimeMonitoringResponse;
import com.seali.gas.entity.response.DeviceStatisticsByTypeResponse;
import com.seali.gas.entity.response.DeviceStatisticsResponse;
import com.seali.gas.pojo.UsmMonitorDevice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 监测设备信息 Dao 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-19
 */
public interface UsmMonitorDeviceMapper extends BaseMapper<UsmMonitorDevice> {

    /**
     * 查询设备实时监测数据
     * 当传入page参数时自动进行分页查询
     *
     * @param page 分页参数（可选）
     * @param request 查询条件
     * @return 设备实时监测数据列表或分页列表
     */
    List<DeviceRealTimeMonitoringResponse> queryDeviceRealTimeMonitoring(@Param("page") IPage<DeviceRealTimeMonitoringResponse> page, @Param("request") EquipmentOperationMonitoringRequest request);


    DeviceRealTimeMonitoringResponse queryDeviceRealTimeMonitoringById(String indexCode);

    /**
     * 根据经纬度更新空间坐标
     *
     * @param id 设备ID
     * @param longitude 经度
     * @param latitude 纬度
     * @return 影响行数
     */
    int updateGeomByLongLat(@Param("id") String id, @Param("longitude") String longitude, @Param("latitude") String latitude);

    /**
     * 根据WKT文本更新空间坐标
     *
     * @param id 设备ID
     * @param wkt WKT格式的几何数据
     * @return 影响行数
     */
    int updateGeomByWkt(@Param("id") String id, @Param("wkt") String wkt);

    /**
     * 根据空间范围查询设备
     *
     * @param longitude 中心点经度
     * @param latitude 中心点纬度
     * @param distance 距离（米）
     * @return 设备列表
     */
    List<UsmMonitorDevice> findDevicesWithinDistance(
            @Param("longitude") String longitude,
            @Param("latitude") String latitude,
            @Param("distance") double distance);

    /**
     * 根据多边形范围查询设备
     *
     * @param wktPolygon WKT格式的多边形数据
     * @return 设备列表
     */
    List<UsmMonitorDevice> findDevicesWithinPolygon(@Param("wktPolygon") String wktPolygon);

    /**
     * 统计设备数据：全部设备数、正常设备数、离线设备数、报警设备数
     *
     * @return 设备统计数据对象
     */
    DeviceStatisticsResponse queryDeviceStatistics();

    /**
     * 按设备类型统计设备数据：全部设备数、正常设备数、离线设备数、报警设备数
     *
     * @return 按设备类型分组的设备统计数据列表
     */
    List<DeviceStatisticsByTypeResponse> queryDeviceStatisticsByType();

    int deleteByDataId(String dataId);
}
