package com.seali.gas.dao;

import com.seali.gas.dto.UsmFzAlarmAnalysisDto;
import com.seali.gas.entity.response.risk.monitor.*;
import com.seali.gas.form.UsmFzAlarmAnalysisForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 辅助决策-燃气泄露报警统计分析Mapper接口
 */
@Mapper
public interface UsmFzAlarmAnalysisMapper {

    /**
     * 获取报警趋势
     *
     * @param startDate
     * @param endDate
     * @return 报警趋势列表
     */
    List<AlarmStatisticsResponse.AlarmInfo> getAlarmTrends(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取报警等级统计
     *
     * @param startDate
     * @param endDate
     * @return 报警等级统计列表
     */
    List<AlarmLevelStatisticsResponse.AlarmLevelStatistics> getAlarmLevelCounts(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取报警趋势数据
     *
     * @param startDate
     * @param endDate
     * @return 报警趋势数据列表
     */
    List<AlarmTrendStatisticsResponse.DailyAlarmStatistics> getAlarmTrendData(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取高发报警设备列表
     *
     * @param startDate
     * @param endDate
     * @return 高发报警设备列表
     */
    List<AlarmHighFrequencyAlarmDeviceResponse> getHighFrequencyAlarmDevices(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取监测报警统计信息
     * @param startDate
     * @param endDate
     * @return
     */
    UsmFzAlarmAnalysisDto countAlarmsByStatus(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 根据起止日期获取报警数量
     * @param startDate
     * @param endDate
     * @return
     */
    Long countAlarmsByPeriod(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("alarmStatus") Integer alarmStatus);

    /**
     * 根据起止日期获取平均处置时长
     * @param startDate
     * @param endDate
     * @return
     */
    List<Double> getHandlingDurations(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取处置、误报率信息
     * @param request
     * @return
     */
    AlarmDisposalSituationResponse getAlarmDisposalSituation(UsmFzAlarmAnalysisForm request);

    /**
     * 获取企业报警信息统计
     * @param startDate
     * @param endDate
     * @return
     */
    List<AlarmEnterPriseStatisticsResponse> getEnterpriseStatistics(@Param("startDate") Date startDate, @Param("endDate")Date endDate);
}