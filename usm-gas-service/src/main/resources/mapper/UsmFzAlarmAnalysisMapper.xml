<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.gas.dao.UsmFzAlarmAnalysisMapper">

    <!-- 获取监测报警统计信息 -->
    <select id="countAlarmsByStatus" parameterType="com.seali.gas.form.UsmFzAlarmAnalysisForm" resultType="com.seali.gas.dto.UsmFzAlarmAnalysisDto">
        SELECT
            COUNT(*) FILTER (WHERE alarm_status = 9201) AS pendingConfirm,
            COUNT(*) FILTER (WHERE alarm_status  = 9202 ) AS falseAlarm,
            COUNT(*) FILTER (WHERE alarm_status = 9203) AS pendingHandle,
            COUNT(*) FILTER (WHERE alarm_status = 9204) AS handling,
            COUNT(*) FILTER (WHERE alarm_status  IN ('9202', '9205') ) AS handled,
            COUNT(*) AS totalAlarms
        FROM usm_monitor_alarm
        WHERE is_deleted = false
        <if test="startDate != null and endDate != null">
            AND alarm_time BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>

    <!-- 根据起止日期获取报警数量 -->
    <select id="countAlarmsByPeriod" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM usm_monitor_alarm
        WHERE alarm_time BETWEEN #{startDate} AND #{endDate}
        <if test="alarmStatus != null">
            AND alarm_status = #{alarmStatus}
        </if>
          AND is_deleted = false
    </select>

    <!-- 获取报警平均处置时长 -->
    <select id="getHandlingDurations" parameterType="com.seali.gas.form.UsmFzAlarmAnalysisForm" resultType="java.lang.Double">
        SELECT
            EXTRACT(EPOCH FROM (update_time - alarm_time))/3600 AS handling_hours
        FROM usm_monitor_alarm
        WHERE alarm_status in ('9202', '9205')
            <if test="startDate != null and endDate != null">
                AND alarm_time BETWEEN #{startDate} AND #{endDate}
            </if>
          AND is_deleted = false
    </select>

    <!-- 获取报警等级统计 -->
    <select id="getAlarmLevelCounts" resultType="com.seali.gas.entity.response.risk.monitor.AlarmLevelStatisticsResponse$AlarmLevelStatistics">
        SELECT
            a.alarm_level AS "alarmLevel",
            a.alarm_level_name AS "alarmLevelName",
            COUNT(1) AS "totalCount",
            COUNT(CASE WHEN a.alarm_status IN ('9202', '9205') THEN 1 END) AS "handledCount"
        FROM usm_monitor_alarm a
        WHERE a.is_deleted = false
        <if test="startDate != null and endDate != null">
            AND a.alarm_time BETWEEN #{startDate} AND #{endDate}
        </if>
        GROUP BY a.alarm_level, a.alarm_level_name
        ORDER BY a.alarm_level ASC
    </select>

    <!-- 获取报警趋势数据 -->
    <select id="getAlarmTrendData" resultType="com.seali.gas.entity.response.risk.monitor.AlarmTrendStatisticsResponse$DailyAlarmStatistics">
        SELECT
            DATE_TRUNC('day', alarm_time)::date AS date,
            COUNT(1) AS "totalCount",
            COUNT(CASE WHEN alarm_level = 9101 THEN 1 END) AS "level1Count",
            COUNT(CASE WHEN alarm_level = 9102 THEN 1 END) AS "level2Count",
            COUNT(CASE WHEN alarm_level = 9103 THEN 1 END) AS "level3Count",
            COUNT(CASE WHEN alarm_level = 9104 THEN 1 END) AS "level4Count"
        FROM usm_monitor_alarm
        WHERE is_deleted = false
            <if test="startDate != null and endDate != null">
                AND alarm_time BETWEEN #{startDate} AND #{endDate}
            </if>
        GROUP BY DATE_TRUNC('day', alarm_time)::date
        ORDER BY date ASC
    </select>

    <!-- 获取高发报警设备列表 -->
    <select id="getHighFrequencyAlarmDevices" resultType="com.seali.gas.entity.response.risk.monitor.AlarmHighFrequencyAlarmDeviceResponse">
        SELECT
            d.index_code AS "deviceCode",
            d.device_name AS "deviceName",
            d.device_type AS "deviceType",
            d.device_type_name AS "deviceTypeName",
            COUNT(a.id) AS "alarmCount",
            COUNT(CASE WHEN a.alarm_status IN ('9202', '9205') THEN 1 END) AS "handledCount",
            COALESCE(ROUND(CAST(COUNT(CASE WHEN a.alarm_status IN ('9202', '9205') THEN 1 END) AS NUMERIC) /
                           NULLIF(COUNT(a.id), 0) * 100, 2), 0) AS "handleRate",
            -- Level 1 alarms
            COUNT(CASE WHEN a.alarm_level = 9101 THEN 1 END) AS "level1Count",
            COUNT(CASE WHEN a.alarm_level = 9101 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS "level1HandledCount",
            COALESCE(ROUND(CAST(COUNT(CASE WHEN a.alarm_level = 9101 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS NUMERIC) /
                           NULLIF(COUNT(CASE WHEN a.alarm_level = 9101 THEN 1 END), 0) * 100, 2), 0) AS "level1HandleRate",
            -- Level 2 alarms
            COUNT(CASE WHEN a.alarm_level = 9102 THEN 1 END) AS "level2Count",
            COUNT(CASE WHEN a.alarm_level = 9102 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS "level2HandledCount",
            COALESCE(ROUND(CAST(COUNT(CASE WHEN a.alarm_level = 9102 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS NUMERIC) /
                           NULLIF(COUNT(CASE WHEN a.alarm_level = 9102 THEN 1 END), 0) * 100, 2), 0) AS "level2HandleRate",
            -- Level 3 alarms
            COUNT(CASE WHEN a.alarm_level = 9103 THEN 1 END) AS "level3Count",
            COUNT(CASE WHEN a.alarm_level = 9103 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS "level3HandledCount",
            COALESCE(ROUND(CAST(COUNT(CASE WHEN a.alarm_level = 9103 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS NUMERIC) /
                           NULLIF(COUNT(CASE WHEN a.alarm_level = 9103 THEN 1 END), 0) * 100, 2), 0) AS "level3HandleRate"
        FROM usm_monitor_device d
                 LEFT JOIN usm_monitor_alarm a ON d.data_id = a.device_id
        WHERE a.is_deleted = false
            <if test="startDate != null and endDate != null">
                AND a.alarm_time BETWEEN #{startDate} AND #{endDate}
            </if>
        GROUP BY
            d.index_code,
            d.device_name,
            d.device_type,
            d.device_type_name
        HAVING
            COUNT(a.id) > 0
        ORDER BY
            "alarmCount" DESC
    </select>

    <!-- 获取企业报警信息统计 -->
    <select id="getEnterpriseStatistics" resultType="com.seali.gas.entity.response.risk.monitor.AlarmEnterPriseStatisticsResponse">
        SELECT
            d.ownership_unit_name AS "enterpriseName",
            COUNT(a.id) AS "alarmCount",
            COUNT(CASE WHEN a.alarm_status IN ('9202', '9205') THEN 1 END) AS "handledCount",
            COALESCE(ROUND(CAST(COUNT(CASE WHEN a.alarm_status IN ('9202', '9205') THEN 1 END) AS NUMERIC) /
                           NULLIF(COUNT(a.id), 0) * 100, 2), 0) AS "handleRate",
            -- Level 1 alarms
            COUNT(CASE WHEN a.alarm_level = 9101 THEN 1 END) AS "level1Count",
            COUNT(CASE WHEN a.alarm_level = 9101 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS "level1HandledCount",
            COALESCE(ROUND(CAST(COUNT(CASE WHEN a.alarm_level = 9101 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS NUMERIC) /
                           NULLIF(COUNT(CASE WHEN a.alarm_level = 9101 THEN 1 END), 0) * 100, 2), 0) AS "level1HandleRate",
            -- Level 2 alarms
            COUNT(CASE WHEN a.alarm_level = 9102 THEN 1 END) AS "level2Count",
            COUNT(CASE WHEN a.alarm_level = 9102 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS "level2HandledCount",
            COALESCE(ROUND(CAST(COUNT(CASE WHEN a.alarm_level = 9102 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS NUMERIC) /
                           NULLIF(COUNT(CASE WHEN a.alarm_level = 9102 THEN 1 END), 0) * 100, 2), 0) AS "level2HandleRate",
            -- Level 3 alarms
            COUNT(CASE WHEN a.alarm_level = 9103 THEN 1 END) AS "level3Count",
            COUNT(CASE WHEN a.alarm_level = 9103 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS "level3HandledCount",
            COALESCE(ROUND(CAST(COUNT(CASE WHEN a.alarm_level = 9103 AND a.alarm_status IN ('9202', '9205') THEN 1 END) AS NUMERIC) /
                           NULLIF(COUNT(CASE WHEN a.alarm_level = 9103 THEN 1 END), 0) * 100, 2), 0) AS "level3HandleRate"
        FROM usm_monitor_device d
                 LEFT JOIN usm_monitor_alarm a ON d.data_id = a.device_id
            AND a.is_deleted = false
        where d.ownership_unit_name IS NOT null
          <if test="startDate != null and endDate != null">
              AND a.alarm_time BETWEEN #{startDate} AND #{endDate}
          </if>
        GROUP BY
            d.ownership_unit_name
        HAVING
            COUNT(a.id) > 0
        ORDER BY
            "alarmCount" DESC;
    </select>

</mapper>