<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.gas.dao.UsmMonitorDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seali.gas.pojo.UsmMonitorDevice">
        <id column="id" property="id" />
        <result column="data_id" property="dataId" />
        <result column="index_code" property="indexCode" />
        <result column="device_name" property="deviceName" />
        <result column="device_type" property="deviceType" />
        <result column="device_type_name" property="deviceTypeName" />
        <result column="monitor_type" property="monitorType" />
        <result column="monitor_type_name" property="monitorTypeName" />
        <result column="monitor_index" property="monitorIndex" />
        <result column="monitor_index_name" property="monitorIndexName" />
        <result column="monitor_target" property="monitorTarget" />
        <result column="monitor_target_name" property="monitorTargetName" />
        <result column="monitor_object_id" property="monitorObjectId" />
        <result column="monitor_object_name" property="monitorObjectName" />
        <result column="collect_frequency" property="collectFrequency" />
        <result column="upload_frequency" property="uploadFrequency" />
        <result column="measure_range_low" property="measureRangeLow" />
        <result column="measure_range_up" property="measureRangeUp" />
        <result column="measure_unit" property="measureUnit" />
        <result column="region_code" property="regionCode" />
        <result column="region_name" property="regionName" />
        <result column="region_path" property="regionPath" />
        <result column="region_path_name" property="regionPathName" />
        <result column="address" property="address" />
        <result column="geom_text" property="geomText" />
        <result column="geom" property="geom" typeHandler="com.seali.gas.handler.PostGisGeometryTypeHandler" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="online_status" property="onlineStatus" />
        <result column="is_vss" property="isVss" />
        <result column="pic_urls" property="picUrls" />
        <result column="time" property="time" />
    </resultMap>

    <resultMap id="DeviceRealTimeMonitoringMap" type="com.seali.gas.entity.response.DeviceRealTimeMonitoringResponse">
        <result column="id" property="id" />
        <result column="monitor_source" property="monitorSource" />
        <result column="index_code" property="indexCode" />
        <result column="device_name" property="deviceName" />
        <result column="device_type" property="deviceType" />
        <result column="device_type_name" property="deviceTypeName" />
        <result column="monitor_object_id" property="monitorObjectId" />
        <result column="monitor_object_name" property="monitorObjectName" />
        <result column="monitor_data_id" property="monitorDataId" />
        <result column="value" property="value" />
        <result column="monitor_time" property="monitorTime" />
        <result column="work_status" property="workStatus" />
        <result column="online_status" property="onlineStatus" />
        <result column="address" property="address" />
        <result column="latitude" property="latitude" />
        <result column="longitude" property="longitude" />
    </resultMap>

    <!-- 设备统计响应映射 -->
    <resultMap id="DeviceStatisticsMap" type="com.seali.gas.entity.response.DeviceStatisticsResponse">
        <result column="total_device_count" property="totalDeviceCount" />
        <result column="normal_device_count" property="normalDeviceCount" />
        <result column="offline_device_count" property="offlineDeviceCount" />
        <result column="alarm_device_count" property="alarmDeviceCount" />
        <result column="online_rate" property="onlineRate" />
    </resultMap>

    <!-- 按设备类型统计响应映射 -->
    <resultMap id="DeviceStatisticsByTypeMap" type="com.seali.gas.entity.response.DeviceStatisticsByTypeResponse">
        <result column="type" property="type" />
        <result column="type_name" property="typeName" />
        <result column="total_device_count" property="totalDeviceCount" />
        <result column="normal_device_count" property="normalDeviceCount" />
        <result column="offline_device_count" property="offlineDeviceCount" />
        <result column="alarm_device_count" property="alarmDeviceCount" />
        <result column="online_rate" property="onlineRate" />
    </resultMap>
    <delete id="deleteByDataId">
        DELETE FROM usm_monitor_device WHERE data_id = #{dataId}
    </delete>
    <!--
     aoruideMethane	            澳瑞德固定点式激光甲烷气体监测仪
     laserMethane	            固定点式激光甲烷气体监测仪
     wbFixedCh4GrassDetector	万宾固定式激光甲烷气体监测仪
    -->
    <select id="queryDeviceRealTimeMonitoring" resultMap="DeviceRealTimeMonitoringMap">
        WITH B AS (
            SELECT DISTINCT ON (index_code) *
            FROM usm_fixed_point_laser_methane_monitor
            ORDER BY index_code, monitor_time DESC
        )
        SELECT
            A.data_id as id,
            '系统监测' AS monitor_source,
            A.index_code,
            A.device_name,
            A.device_type,
            A.device_type_name,
            A.monitor_index,
            A.monitor_index_name,
            A.monitor_object_id,
            A.monitor_object_name,
            B.id AS monitor_data_id,
            B.methane_concentration AS value,
            B.monitor_time,
            B.work_status,
            A.online_status,
            A.address,
            A.ownership_unit,
            A.ownership_unit_name,
            A.latitude,
            A.longitude
        FROM usm_monitor_device A
        LEFT JOIN B ON A.index_code = B.index_code
        <where>
            <if test="request != null">
                <if test="request.deviceType != null and request.deviceType != ''">
                    AND A.device_type = #{request.deviceType}
                </if>
                <if test="request.managementUnit != null and request.managementUnit != ''">
                    AND A.ownership_unit = #{request.managementUnit}
                </if>
                <if test="request.managementUnitName != null and request.managementUnitName != ''">
                    AND A.ownership_unit_name = #{request.managementUnitName}
                </if>
                <if test="request.town != null and request.town != ''">
                    AND A.region_code = #{request.town}
                </if>
                <if test="request.onlineStatus != null">
                    AND A.online_status = #{request.onlineStatus}
                </if>
                <if test="request.keyWord != null and request.keyWord != ''">
                    AND (A.index_code like concat('%', #{request.keyWord}, '%') or A.device_name like concat('%', #{request.keyWord}, '%'))
                </if>
            </if>
        </where>
        ORDER BY B.monitor_time DESC
    </select>

    <!-- 根据设备id查询设备监测数据 -->
    <select id="queryDeviceRealTimeMonitoringById" resultMap="DeviceRealTimeMonitoringMap">
            SELECT
                A.data_id as id,
                '系统监测' AS monitor_source,
                A.index_code,
                A.device_name,
                A.device_type,
                A.device_type_name,
                A.monitor_object_id,
                A.monitor_object_name,
                B.id AS monitor_data_id,
                B.methane_concentration AS value,
                B.monitor_time,
                B.work_status,
                A.online_status,
                A.address,
                A.latitude,
                A.longitude
            FROM usm_monitor_device A
            LEFT JOIN usm_fixed_point_laser_methane_monitor B ON A.index_code = B.index_code
            WHERE A.data_id = #{deviceId}
            ORDER BY B.monitor_time DESC
            LIMIT 1
    </select>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        data_id as id, index_code, device_name, device_type, device_type_name, monitor_type, monitor_type_name,
        monitor_index, monitor_index_name, monitor_target, monitor_target_name, monitor_object_id,
        monitor_object_name, collect_frequency, upload_frequency, measure_range_low, measure_range_up,
        measure_unit, region_code, region_name, region_path, region_path_name, address,ST_AsText(geom) as geom_text, geom, longitude,
        latitude, online_status, is_vss, pic_urls, time, create_time, update_time, create_by, update_by
    </sql>

    <!-- 带空间数据转换的查询结果列 -->
    <sql id="Base_Column_List_With_Geom_Text">
        data_id as id, index_code, device_name, device_type, device_type_name, monitor_type, monitor_type_name,
        monitor_index, monitor_index_name, monitor_target, monitor_target_name, monitor_object_id,
        monitor_object_name, collect_frequency, upload_frequency, measure_range_low, measure_range_up,
        measure_unit, region_code, region_name, region_path, region_path_name, address,
        ST_AsText(ST_SetSRID(geom, 0)) as geom_text, geom, longitude, latitude, online_status, is_vss, pic_urls,
        time, create_time, update_time, create_by, update_by
    </sql>

    <!-- 根据经纬度更新空间坐标 -->
    <update id="updateGeomByLongLat">
        UPDATE usm_monitor_device
        SET geom = ST_SetSRID(ST_MakePoint(#{longitude}::float, #{latitude}::float), 4326)
        WHERE data_id = #{id}
    </update>

    <!-- 根据WKT文本更新空间坐标 -->
    <update id="updateGeomByWkt">
        UPDATE usm_monitor_device
        SET geom = ST_GeomFromText(#{wkt}, 4326)
        WHERE data_id = #{id}
    </update>

    <!-- 根据空间范围查询设备 -->
    <select id="findDevicesWithinDistance" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List_With_Geom_Text"/>
        FROM usm_monitor_device
        WHERE ST_DWithin(
            geom::geography,
            ST_SetSRID(ST_MakePoint(#{longitude}::float, #{latitude}::float), 4326)::geography,
            #{distance}
        )
    </select>

    <!-- 根据多边形范围查询设备 -->
    <select id="findDevicesWithinPolygon" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List_With_Geom_Text"/>
        FROM usm_monitor_device
        WHERE ST_Within(
            geom,
            ST_GeomFromText(#{wktPolygon}, 4326)
        )
    </select>

    <!-- 统计设备数据：全部设备数、正常设备数、离线设备数、报警设备数 -->
    <select id="queryDeviceStatistics" resultMap="DeviceStatisticsMap">
        WITH alarm_devices AS (
            -- 查询当前有报警的设备（未删除且未处理完成的报警）
            SELECT DISTINCT device_id
            FROM usm_monitor_alarm
            WHERE is_deleted = false
            AND alarm_status NOT IN (9202, 9205, 9206) -- 排除误报、已处置和已归档的报警
        ),
        device_stats AS (
            SELECT
                -- 全部设备数
                COUNT(d.data_id) AS total_device_count,

                -- 正常设备数（在线且无报警）
                COUNT(CASE WHEN d.online_status = 1 AND a.device_id IS NULL THEN 1 END) AS normal_device_count,

                -- 离线设备数（所有离线设备，无论是否有报警）
                COUNT(CASE WHEN d.online_status = 0 OR d.online_status IS NULL THEN 1 END) AS offline_device_count,

                -- 报警设备数（在线且有未处理的报警）
                COUNT(CASE WHEN d.online_status = 1 AND a.device_id IS NOT NULL THEN 1 END) AS alarm_device_count
            FROM
                usm_monitor_device d
            LEFT JOIN
                alarm_devices a ON d.data_id = a.device_id
        )
        SELECT
            total_device_count,
            normal_device_count,
            offline_device_count,
            alarm_device_count,
            -- 计算在线率
            CASE
                WHEN total_device_count > 0 THEN
                    ROUND(CAST((total_device_count - offline_device_count) AS NUMERIC) / total_device_count * 100, 2)
                ELSE 0
            END AS online_rate
        FROM device_stats
    </select>

    <!-- 按设备类型统计设备数据：全部设备数、正常设备数、离线设备数、报警设备数 -->
    <select id="queryDeviceStatisticsByType" resultMap="DeviceStatisticsByTypeMap">
        WITH alarm_devices AS (
            -- 查询当前有报警的设备（未删除且未处理完成的报警）
            SELECT DISTINCT device_id
            FROM usm_monitor_alarm
            WHERE is_deleted = false
            AND alarm_status NOT IN (9202, 9205, 9206) -- 排除误报、已处置和已归档的报警
        ),
        device_stats_by_type AS (
            SELECT
                d.device_type as type,
                d.device_type_name as type_name,
                -- 全部设备数
                COUNT(d.data_id) AS total_device_count,

                -- 正常设备数（在线且无报警）
                COUNT(CASE WHEN d.online_status = 1 AND a.device_id IS NULL THEN 1 END) AS normal_device_count,

                -- 离线设备数（所有离线设备，无论是否有报警）
                COUNT(CASE WHEN d.online_status = 0 OR d.online_status IS NULL THEN 1 END) AS offline_device_count,

                -- 报警设备数（在线且有未处理的报警）
                COUNT(CASE WHEN d.online_status = 1 AND a.device_id IS NOT NULL THEN 1 END) AS alarm_device_count
            FROM
                usm_monitor_device d
            LEFT JOIN
                alarm_devices a ON d.data_id = a.device_id
            GROUP BY
                d.device_type, d.device_type_name
        )
        SELECT
            type,
            type_name,
            total_device_count,
            normal_device_count,
            offline_device_count,
            alarm_device_count,
            -- 计算在线率
            CASE
                WHEN total_device_count > 0 THEN
                    ROUND(CAST((total_device_count - offline_device_count) AS NUMERIC) / total_device_count * 100, 2)
                ELSE 0
            END AS online_rate
        FROM device_stats_by_type
        ORDER BY type
    </select>
</mapper>
