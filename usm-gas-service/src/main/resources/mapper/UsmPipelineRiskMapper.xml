<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.gas.dao.UsmPipelineRiskMapper">

    <!-- 获取管线风险统计信息 -->
    <select id="getPipelineRiskStatistics" resultType="com.seali.gas.entity.response.risk.RiskPipelineRiskStatisticsResponse">
        SELECT
            ROUND(COALESCE(SUM(CASE WHEN e.risk_level = 7001 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "bigRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN e.risk_level = 7002 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "largerRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN e.risk_level = 7003 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "generalRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN e.risk_level = 7004 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "lowRiskLength"
        FROM usm_zy_gas_pipeline p
                 LEFT JOIN usm_risk_assessment_pipeline e ON p.id = e.pipeline_id::bigint
        WHERE p.is_deleted = false
    </select>

    <!-- 获取企业管线风险统计数据 -->
    <select id="getEnterprisePipelineRiskStatisticsList" resultType="com.seali.gas.entity.response.risk.RiskEnterprisePipelineRiskStatisticsResponse$EnterpriseRiskStatistics">
        SELECT
            p.management_unit AS "enterpriseCode",
            p.management_unit_name AS "enterpriseName",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7001 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "bigRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7002 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "largerRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7003 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "generalRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7004 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "lowRiskLength"
        FROM usm_zy_gas_pipeline p
                 LEFT JOIN usm_risk_assessment_pipeline r ON p.id = r.pipeline_id::bigint
        WHERE p.is_deleted = false
        GROUP BY p.management_unit, p.management_unit_name
    </select>


    <!-- 获取区域管线风险统计信息 -->
    <select id="getRegionPipelineRiskStatistics" resultType="com.seali.gas.entity.response.risk.RiskRegionPipelineRiskStatisticsResponse$RegionRiskStatistics">
        SELECT
            p.town AS "regionCode",
            p.town_name AS "regionName",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7001 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "bigRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7002 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "largerRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7003 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "generalRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7004 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "lowRiskLength"
        FROM usm_zy_gas_pipeline p
                 LEFT JOIN usm_risk_assessment_pipeline r ON p.id = r.pipeline_id::bigint
        WHERE p.is_deleted = false
        GROUP BY p.town, p.town_name
    </select>

    <!-- 获取场站风险统计信息 -->
    <select id="getStationRiskStatistics" resultType="com.seali.gas.entity.response.risk.RiskStationRiskStatisticsResponse$StationRiskStatistics">
        SELECT
            p.station_code AS "stationCode",
            p.station_name AS "stationName",
            COALESCE(SUM(CASE WHEN r.risk_level = 7001 THEN 1 ELSE 0 END), 0) AS "bigRiskCount",
            COALESCE(SUM(CASE WHEN r.risk_level = 7002 THEN 1 ELSE 0 END), 0) AS "largerRiskCount",
            COALESCE(SUM(CASE WHEN r.risk_level = 7003 THEN 1 ELSE 0 END), 0) AS "generalRiskCount",
            COALESCE(SUM(CASE WHEN r.risk_level = 7004 THEN 1 ELSE 0 END), 0) AS "lowRiskCount"
        FROM usm_zy_gas_station p
                 LEFT JOIN usm_risk_assessment_station r ON p.id = r.station_id::bigint
        WHERE p.is_deleted = false
        GROUP BY p.station_code, p.station_name
    </select>

    <!-- 获取管线风险清单数据 -->
    <select id="getPipelineRiskListItems" resultType="com.seali.gas.entity.response.risk.RiskPipelineRiskListResponse">
        SELECT
            p.id AS "id",
            p.pipeline_code AS "pipelineCode",
            (EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM p.construction_time)) AS "pipelineAge",
            p.material AS "pipelineMaterial",
            p.material_name AS "pipelineMaterialName",
            p.pipe_diameter AS "pipelineDiameter",
            r.risk_level AS "riskLevel",
            r.risk_level_name AS "riskLevelName"
        FROM usm_zy_gas_pipeline p
                 LEFT JOIN usm_risk_assessment_pipeline r ON p.id = r.pipeline_id::bigint
        WHERE p.is_deleted = false
        ORDER BY r.risk_level
    </select>

    <!-- 获取场站风险清单数据 -->
    <select id="getStationRiskListItems" resultType="com.seali.gas.entity.response.risk.RiskStationRiskListResponse">
        SELECT
            s.id AS "id",
            s.station_code AS "stationCode",
            s.station_name AS "stationName",
            s.station_type AS "stationType",
            s.station_type_name AS "stationTypeName",
            r.risk_level AS "riskLevel",
            r.risk_level_name AS "riskLevelName"
        FROM usm_zy_gas_station s
                 LEFT JOIN usm_risk_assessment_station r ON s.id = r.station_id::bigint
        WHERE s.is_deleted = false
        ORDER BY r.risk_level
    </select>

    <!-- 按压力级别统计企业管线风险数据 -->
    <select id="getPressurePipelineRiskStatistics" resultType="com.seali.gas.entity.response.risk.RiskPressurePipelineRiskStatisticsResponse$PressureRiskStatistics">
        SELECT
            p.pressure_level AS "pressureLevel",
            p.pressure_level_name AS "pressureLevelName",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7001 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "bigRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7002 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "largerRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7003 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "generalRiskLength",
            ROUND(COALESCE(SUM(CASE WHEN r.risk_level = 7004 THEN ST_Length(p.geom::geography)/1000 ELSE 0 END), 0)::numeric, 2) AS "lowRiskLength"
        FROM usm_zy_gas_pipeline p
                 LEFT JOIN usm_risk_assessment_pipeline r ON p.id = r.pipeline_id::bigint
        WHERE p.is_deleted = false
        GROUP BY p.pressure_level, p.pressure_level_name
    </select>

</mapper>