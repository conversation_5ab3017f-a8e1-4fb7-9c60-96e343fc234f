<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.gas.dao.UsmMonitorRecordMapper">

    <!-- 根据设备id查询设备监测曲线 -->
    <select id="monitorCurve" resultType="com.seali.gas.dto.UsmMonitorRecordDto">
        select umr.monitor_time,umr.monitor_value,umr.monitor_index,umd.device_name as deviceName
        from usm_monitor_record umr left join usm_monitor_device umd on umr.device_id = umd.data_id
        where umr.device_id = #{deviceId}
        <if test="startTime != null">
            and umr.monitor_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and umr.monitor_time &lt;= #{endTime}
        </if>
        order by umr.monitor_time
    </select>

    <!-- 根据设备id查询设备监测曲线（分页） -->
    <select id="monitorCurvePage" resultType="com.seali.gas.dto.UsmMonitorRecordDto">
        select umr.monitor_time,umr.monitor_value,umr.monitor_index,umd.device_name as deviceName
        from usm_monitor_record umr left join usm_monitor_device umd on umr.device_id = umd.data_id
        where umr.device_id = #{request.deviceId} and monitor_status = 9001
        <if test="request.startTime != null">
            and umr.monitor_time &gt;= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            and umr.monitor_time &lt;= #{request.endTime}
        </if>
        order by umr.monitor_time desc
    </select>

    <select id="queryDeviceRecords" resultType="com.seali.gas.pojo.UsmMonitorRecord">
        SELECT t.*
        FROM usm_monitor_record t
        WHERE t.device_id = #{deviceId} and t.monitor_time >= (
            SELECT monitor_time
            FROM usm_monitor_record
            WHERE device_id = #{deviceId}
            and monitor_status = 9002 AND (offline_time IS NULL OR recovery_time IS NULL)
            ORDER BY monitor_time
            LIMIT 1
            )
        ORDER BY t.monitor_time;
    </select>

    <!-- 查询设备离线记录（分页） -->
    <select id="queryDeviceOfflineRecords" resultType="com.seali.gas.entity.response.DeviceOfflineRecordResponse">
        select
            umr.device_id as deviceId,
            umd.device_name as deviceName,
            COALESCE(umr.offline_time, umr.monitor_time) as offlineTime,
            umr.recovery_time as recoveryTime,
            CASE
            WHEN umr.offline_duration IS NOT NULL AND umr.offline_duration != '' THEN umr.offline_duration
            WHEN umr.recovery_time IS NOT NULL THEN
            CAST(FLOOR(EXTRACT(EPOCH FROM (umr.recovery_time - COALESCE(umr.offline_time, umr.monitor_time)))/60) AS VARCHAR) -- 已恢复，用恢复时间减离线时间，取整
            ELSE
            CAST(FLOOR(EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - COALESCE(umr.offline_time, umr.monitor_time)))/60) AS VARCHAR) -- 未恢复，用当前时间减离线时间，取整
            END as offlineDuration
        from
            usm_monitor_record umr
            left join usm_monitor_device umd on umr.device_id = umd.data_id
        where
            umr.device_id = #{request.deviceId}
            and umr.monitor_status = 9002 -- 离线状态
            and umr.offline_time is not null
        <if test="request.startTime != null">
            and umr.offline_time &gt;= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            and umr.offline_time &lt;= #{request.endTime}
        </if>
        order by
            umr.offline_time desc
    </select>
</mapper>
