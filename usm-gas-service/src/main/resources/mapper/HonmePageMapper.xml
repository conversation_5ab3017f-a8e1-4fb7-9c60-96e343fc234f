<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.gas.dao.HomePageMapper">

    <!-- 根据压力级别统计管线长度 -->
    <select id="pipelineStatisticsByPressure" resultType="com.seali.gas.dto.UsmZyGasPipelineDto">
        SELECT
            ROUND(SUM(a.pipe_length), 2) AS totalLength,
            ROUND(SUM(CASE WHEN a.pressure_level = 1001 THEN a.pipe_length ELSE 0 END), 2) AS lowLength,
            ROUND(SUM(CASE WHEN a.pressure_level = 1002 THEN a.pipe_length ELSE 0 END), 2) AS mediumLength,
            ROUND(SUM(CASE WHEN a.pressure_level = 1003 THEN a.pipe_length ELSE 0 END), 2) AS highLength
        FROM usm_zy_gas_pipeline a where a.is_deleted = false
    </select>

    <!-- 查询管网维修次数 -->
    <select id="getRepairCount" resultType="java.lang.Integer">
        select count(1) as repairCount from usm_zy_gas_repair where is_deleted = false
    </select>

    <!-- 今日、本月报警数量统计 -->
    <select id="alarmCount" resultType="com.seali.gas.entity.response.HomePageResponse">
        SELECT
        (SELECT COUNT(*)
        FROM usm_gas.usm_monitor_alarm
        WHERE alarm_time::date = CURRENT_DATE
        AND is_deleted = false) AS todayCount,
        (SELECT COUNT(*)
        FROM usm_gas.usm_monitor_alarm
        WHERE alarm_time &gt;= date_trunc('month', CURRENT_DATE)
        AND alarm_time &lt; date_trunc('month', CURRENT_DATE) + interval '1 month'
        AND is_deleted = false) AS monthCount
    </select>

    <!-- 根据报警等级统计待处置报警数量 -->
    <select id="alarmStatisticsByLevel" resultType="com.seali.gas.entity.response.HomePageResponse">
        SELECT
            COUNT(CASE WHEN alarm_level = 9101 THEN 1 END) AS level1count,
            COUNT(CASE WHEN alarm_level = 9102 THEN 1 END) AS level2count,
            COUNT(CASE WHEN alarm_level = 9103 THEN 1 END) AS level3count
        FROM
            usm_gas.usm_monitor_alarm
        WHERE
            is_deleted = false and alarm_status = 9203
    </select>

    <!-- 查询待处置报警列表 -->
    <select id="getAlarmList" resultType="com.seali.gas.entity.response.risk.monitor.AlarmStatisticsResponse$AlarmInfo">
        SELECT
        a.id AS "alarmId",
        a.alarm_code AS "alarmCode",
        d.index_code AS "deviceCode",
        d.device_name AS "deviceName",
        d.device_type AS "deviceType",
        d.device_type_name AS "deviceTypeName",
        a.alarm_level AS "alarmLevel",
        a.alarm_level_name AS "alarmLevelName",
        a.alarm_source AS "alarmSource",
        a.alarm_time AS "alarmTime",
        a.alarm_status AS "handleStatus",
        a.alarm_level_name AS "handleStatusName",
        d.address AS address,
        d.longitude AS longitude,
        d.latitude AS latitude
        FROM usm_monitor_alarm a
        JOIN usm_monitor_device d ON a.device_id = d.data_id
        WHERE a.is_deleted = false and a.alarm_status = 9203
        ORDER BY a.alarm_level, a.alarm_time DESC
    </select>

    <!-- 获取近30日燃气泄漏报警排名 -->
    <select id="getAlarmRank" resultType="com.seali.gas.entity.response.risk.monitor.AlarmHighFrequencyAlarmDeviceResponse">
        SELECT
            d.device_name AS "deviceName",
            d.address AS "address",
            COUNT(a.id) AS "alarmCount"
        FROM usm_monitor_device d
                 LEFT JOIN usm_monitor_alarm a ON d.data_id = a.device_id
        WHERE d.device_type = 'laserMethane'
        GROUP BY
            d.device_name,
            d.address
        HAVING
            COUNT(a.id) > 0
        ORDER BY
            "alarmCount" desc
    </select>

    <!-- 获取报警统计数据 -->
    <select id="getAlarmStatistics" resultType="com.seali.gas.entity.response.HomePageResponse">
        SELECT
            COUNT(*) AS alarmCount,  -- 全部报警数
            COUNT(CASE WHEN alarm_status IN ('9202', '9205') THEN 1 END) AS handleCount,  -- 已处置报警数（9205=已处置）
            ROUND(
                        COUNT(CASE WHEN alarm_status IN ('9202', '9205') THEN 1 END) * 100.0 /
                        NULLIF(COUNT(*), 0),  -- 避免除以0错误
                        2
                ) AS handleRate  -- 处置率（百分比，保留2位小数）
        FROM
            usm_gas.usm_monitor_alarm
        WHERE
            is_deleted = false
    </select>

    <!-- 获取报警趋势数据 -->
    <select id="getAlarmTrendData" resultType="com.seali.gas.entity.response.risk.monitor.AlarmTrendStatisticsResponse$DailyAlarmStatistics">
        SELECT
            DATE_TRUNC('day', alarm_time)::date AS date,
            COUNT(1) AS "totalCount"
        FROM usm_monitor_alarm
        WHERE is_deleted = false
          AND alarm_time >= CURRENT_DATE - (INTERVAL '1 day' * #{days})
        GROUP BY DATE_TRUNC('day', alarm_time)::date
        ORDER BY date ASC
    </select>

</mapper>
