<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.gas.dao.UsmMonitorAlarmMapper">

    <!-- 报警表分页查询 -->
    <select id="findPage" resultType="com.seali.gas.dto.UsmMonitorAlarmDto">
        select uma.id,uma.alarm_source, uma.alarm_code,uma.alarm_time,umd.device_name as deviceCode,umd.monitor_type_name as alarmTypeName,
                umd.monitor_type as alarmType,uma.monitor_object_id,uma.monitor_object_name,uma.alarm_value,uma.alarm_location,uma.alarm_level,
                umd.ownership_unit_name as ownershipUnitName,uma.alarm_status,uma.alarm_status_name,umd.longitude,umd.latitude,uma.device_id
        from usm_monitor_alarm uma left join usm_monitor_device umd on uma.device_id = umd.data_id
        where uma.is_deleted = false
        <if test="alarmSource != null and alarmSource != ''">
            and uma.alarm_source = #{alarmSource}
        </if>
        <if test="alarmLevel != null and alarmLevel != ''">
            and uma.alarm_level = #{alarmLevel}
        </if>
        <if test="alarmType != null">
            and umd.monitor_type = #{alarmType}::varchar
        </if>
        <if test="startTime != null">
            and uma.alarm_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and uma.alarm_time &lt;= #{endTime}
        </if>
        <if test="alarmStatus != null and alarmStatus != ''">
            and uma.alarm_status = #{alarmStatus}
        </if>
        <if test="code != null and code != ''">
            and (uma.alarm_code like concat('%',#{code},'%') or umd.device_name like concat('%',#{code},'%'))
        </if>
    </select>

    <!-- 报警表根据id查询 -->
    <select id="findById" resultType="com.seali.gas.dto.UsmMonitorAlarmDto">
        select uma.alarm_status,uma.alarm_status_name,uma.alarm_code,umd.index_code as deviceCode,umd.device_name as deviceName,umd.monitor_type_name as alarmType,uma.alarm_level,
               uma.monitor_object_id,uma.monitor_object_name,uma.alarm_time,uma.alarm_value,uma.alarm_location,umd.longitude,umd.latitude,uma.device_id,uma.update_time
        from usm_monitor_alarm uma left join usm_monitor_device umd on uma.device_id = umd.data_id
        where uma.is_deleted = false and uma.id = #{id}
    </select>

    <!-- 根据设备id查询报警记录 -->
    <select id="findAlarmRecordByDeviceId" resultType="com.seali.gas.dto.UsmMonitorAlarmDto">
        select uma.alarm_time,uma.alarm_value,uma.alarm_level_name,uma.alarm_status_name
        from usm_monitor_alarm uma
        where uma.is_deleted = false and uma.device_id = #{deviceId}
    </select>

    <!-- 根据报警id查询报警处置记录 -->
    <select id="findAlarmHandleList" resultType="com.seali.gas.dto.UsmMonitorAlarmStatusDto">
        select id,handle_status,handle_status_name,description,handle_user,unit,create_time,pic_urls,remarks
        from usm_monitor_alarm_status where alarm_id = #{id} and alarm_status = 92003
    </select>

    <!-- 根据报警id查询关联处置方案 -->
    <select id="findConnectScheme" resultType="com.seali.gas.dto.UsmFzGasSchemeDto">
        select ufgs.scheme_name,ufgs.source_unit_name,ufgs.file_url from usm_fz_gas_scheme ufgs
            left join usm_monitor_device umd on ufgs.alarm_type::varchar = umd.monitor_type
            left join usm_monitor_alarm uma on umd.data_id = uma.device_id where uma.id = #{id}
    </select>

    <!-- 根据报警id查询关联专家 -->
    <select id="findConnectExpert" resultType="com.seali.gas.dto.UsmFzGasExpertDto">
        select ufge.expert_name,ufge.professional_field,ufge.contact_info from usm_fz_gas_expert ufge
            left join usm_monitor_device umd on ufge.alarm_type::varchar = umd.monitor_type
            left join usm_monitor_alarm uma on umd.data_id = uma.device_id where uma.id = #{id}
    </select>
</mapper>
