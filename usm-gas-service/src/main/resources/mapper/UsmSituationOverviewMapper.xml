<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seali.gas.dao.UsmSituationOverviewMapper">

    <!-- 获取管线总长度 -->
    <select id="getPipelineTotalLength" resultType="java.lang.Double">
        SELECT ROUND(COALESCE(SUM(ST_Length(geom::geography))::numeric, 0), 2)
        FROM usm_zy_gas_pipeline
        WHERE is_deleted = false
    </select>

    <!-- 获取管点总数 -->
    <select id="getPointTotalCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM usm_zy_gas_point
        WHERE is_deleted = false
    </select>

    <!-- 获取场站总数 -->
    <select id="getStationTotalCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM usm_zy_gas_station
        WHERE is_deleted = false
    </select>

    <!-- 获取气井总数 -->
    <select id="getWellTotalCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM usm_zy_gas_well
        WHERE is_deleted = false
    </select>

    <!-- 按材料统计管线长度 -->
    <select id="getPipelineLengthByMaterial" resultType="java.util.Map">
        SELECT material as code,
               material_name as name,
               ROUND(COALESCE(SUM(ST_Length(geom::geography))::numeric, 0), 2) as length
        FROM usm_zy_gas_pipeline
        WHERE is_deleted = false
        GROUP BY material, material_name
    </select>

    <!-- 按管龄统计管线长度 -->
    <select id="getPipelineLengthByAge" resultType="java.util.Map">
        WITH age_ranges AS (
        SELECT '1' as code, '1-5年' as name
        UNION ALL SELECT '6', '6-10年'
        UNION ALL SELECT '11', '11-15年'
        UNION ALL SELECT '15', '15年以上'
        )
        SELECT
        ar.code,
        ar.name,
        ROUND(COALESCE(SUM(ST_Length(p.geom::geography))::numeric, 0), 2) as length
        FROM age_ranges ar
        LEFT JOIN usm_zy_gas_pipeline p ON
        CASE
        WHEN p.construction_time IS NULL THEN '15'
        WHEN (EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM p.construction_time)) <![CDATA[ <= ]]> 5 THEN '1'
        WHEN (EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM p.construction_time)) <![CDATA[ <= ]]> 10 THEN '6'
        WHEN (EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM p.construction_time)) <![CDATA[ <= ]]> 15 THEN '11'
        ELSE '15'
        END = ar.code
        AND p.is_deleted = false
        GROUP BY ar.code, ar.name
        ORDER BY ar.code
    </select>

    <!-- 按企业统计管线长度 -->
    <select id="getPipelineLengthByEnterprise" resultType="java.util.Map">
        SELECT management_unit as code,
               management_unit_name as name,
               ROUND(COALESCE(SUM(ST_Length(geom::geography))::numeric, 0), 2) as length
        FROM usm_zy_gas_pipeline
        WHERE is_deleted = false
        GROUP BY management_unit, management_unit_name
    </select>

    <!-- 按压力级别统计管线长度 -->
    <select id="getPipelineLengthByPressureLevel" resultType="java.util.Map">
        SELECT pressure_level as code,
               pressure_level_name as name,
               ROUND(COALESCE(SUM(ST_Length(geom::geography))::numeric, 0), 2) as length
        FROM usm_zy_gas_pipeline
        WHERE is_deleted = false
        GROUP BY pressure_level, pressure_level_name
    </select>

    <!-- 按类型统计场站数量 -->
    <select id="getStationCountByType" resultType="java.util.Map">
        SELECT station_type as code,
               station_type_name as name,
               COUNT(*) as count
        FROM usm_zy_gas_station
        WHERE is_deleted = false
        GROUP BY station_type, station_type_name
    </select>

    <!-- 按风险等级统计管线长度 -->
    <select id="getPipelineLengthByRiskLevel" resultType="java.util.Map">
        SELECT risk_level as code,
               risk_level_name as name,
               ROUND(COALESCE(SUM(ST_Length(geom::geography))::numeric, 0), 2) as length
        FROM usm_risk_assessment_pipeline
                 left join usm_zy_gas_pipeline on usm_risk_assessment_pipeline.pipeline_id::bigint = usm_zy_gas_pipeline.id
        GROUP BY risk_level, risk_level_name
    </select>

    <!-- 按风险等级统计场站数量 -->
    <select id="getStationCountByRiskLevel" resultType="java.util.Map">
        SELECT risk_level as code,
               risk_level_name as name,
               COUNT(*) as count
        FROM usm_risk_assessment_station
        GROUP BY risk_level, risk_level_name
    </select>

    <!-- 获取监控设备总数 -->
    <select id="getMonitorDeviceTotalCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM usm_monitor_device
    </select>

    <!-- 获取在线监控设备数 -->
    <select id="getMonitorDeviceOnlineCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM usm_monitor_device
        WHERE online_status = '1'
    </select>

    <!-- 按设备类型统计监控设备数量和在线数量 -->
    <select id="getMonitorDeviceCountByType" resultType="java.util.Map">
        SELECT device_type as "deviceType", device_type_name as "deviceTypeName",
               COUNT(*) as count,
               COUNT(CASE WHEN online_status = '1' THEN 1 END) as "onlineCount"
        FROM usm_monitor_device
        GROUP BY device_type, device_type_name
    </select>

    <!-- 获取告警总数 -->
    <select id="getAlarmTotalCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM usm_monitor_alarm
        WHERE is_deleted = false
    </select>

    <!-- 获取已处理告警数 -->
    <select id="getAlarmHandledCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM usm_monitor_alarm
        WHERE is_deleted = false
          AND alarm_status = '2'
    </select>

    <!-- 获取近30天告警趋势 -->
    <select id="getAlarmDailyStatistics" resultType="java.util.Map">
        SELECT TO_CHAR(alarm_time, 'YYYY-MM-DD') as date,
               COUNT(*) as "alarmCount",
               COUNT(CASE WHEN alarm_status = '2' THEN 1 END) as "handledCount"
        FROM usm_monitor_alarm
        WHERE is_deleted = false
          AND alarm_time >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY TO_CHAR(alarm_time, 'YYYY-MM-DD')
        ORDER BY date
    </select>

</mapper>