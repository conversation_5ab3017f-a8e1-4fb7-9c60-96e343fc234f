package com.seali.drain.controller;

import com.seali.drain.pojo.UsmRiskAssessmentFactory;
import com.seali.drain.dto.UsmRiskAssessmentFactoryDto;
import com.seali.drain.form.UsmRiskAssessmentFactoryForm;
import com.seali.drain.service.UsmRiskAssessmentFactoryService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmRiskAssessmentFactory")
@Api(tags = "污水厂风险评估表接口")
public class UsmRiskAssessmentFactoryController extends BaseController<UsmRiskAssessmentFactoryService, UsmRiskAssessmentFactory, UsmRiskAssessmentFactoryForm, UsmRiskAssessmentFactoryDto, String> {

    private final UsmRiskAssessmentFactoryService usmRiskAssessmentFactoryService;

    @Autowired
    public UsmRiskAssessmentFactoryController(UsmRiskAssessmentFactoryService service) {
        super(service);
        this.usmRiskAssessmentFactoryService = service;
    }
}