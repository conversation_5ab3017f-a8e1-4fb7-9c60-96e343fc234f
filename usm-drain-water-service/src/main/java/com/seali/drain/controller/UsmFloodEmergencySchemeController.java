package com.seali.drain.controller;

import com.seali.drain.pojo.UsmFloodEmergencyScheme;
import com.seali.drain.dto.UsmFloodEmergencySchemeDto;
import com.seali.drain.form.UsmFloodEmergencySchemeForm;
import com.seali.drain.service.UsmFloodEmergencySchemeService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmFloodEmergencyScheme")
@Api(tags = "防汛应急辅助决策方案信息表接口")
public class UsmFloodEmergencySchemeController extends BaseController<UsmFloodEmergencySchemeService, UsmFloodEmergencyScheme, UsmFloodEmergencySchemeForm, UsmFloodEmergencySchemeDto, String> {

    private final UsmFloodEmergencySchemeService usmFloodEmergencySchemeService;

    @Autowired
    public UsmFloodEmergencySchemeController(UsmFloodEmergencySchemeService service) {
        super(service);
        this.usmFloodEmergencySchemeService = service;
    }
}