package com.seali.drain.controller;

import com.seali.drain.pojo.UsmBasicFloodPoint;
import com.seali.drain.dto.UsmBasicFloodPointDto;
import com.seali.drain.form.UsmBasicFloodPointForm;
import com.seali.drain.service.UsmBasicFloodPointService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmBasicFloodPoint")
@Api(tags = "易涝点信息表接口")
public class UsmBasicFloodPointController extends BaseController<UsmBasicFloodPointService, UsmBasicFloodPoint, UsmBasicFloodPointForm, UsmBasicFloodPointDto, String> {

    private final UsmBasicFloodPointService usmBasicFloodPointService;

    @Autowired
    public UsmBasicFloodPointController(UsmBasicFloodPointService service) {
        super(service);
        this.usmBasicFloodPointService = service;
    }
}