package com.seali.drain.controller;

import com.seali.drain.pojo.UsmRiskHiddenDanger;
import com.seali.drain.dto.UsmRiskHiddenDangerDto;
import com.seali.drain.form.UsmRiskHiddenDangerForm;
import com.seali.drain.service.UsmRiskHiddenDangerService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmRiskHiddenDanger")
@Api(tags = "排水隐患信息表接口")
public class UsmRiskHiddenDangerController extends BaseController<UsmRiskHiddenDangerService, UsmRiskHiddenDanger, UsmRiskHiddenDangerForm, UsmRiskHiddenDangerDto, String> {

    private final UsmRiskHiddenDangerService usmRiskHiddenDangerService;

    @Autowired
    public UsmRiskHiddenDangerController(UsmRiskHiddenDangerService service) {
        super(service);
        this.usmRiskHiddenDangerService = service;
    }
}