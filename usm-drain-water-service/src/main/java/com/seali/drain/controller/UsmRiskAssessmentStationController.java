package com.seali.drain.controller;

import com.seali.drain.pojo.UsmRiskAssessmentStation;
import com.seali.drain.dto.UsmRiskAssessmentStationDto;
import com.seali.drain.form.UsmRiskAssessmentStationForm;
import com.seali.drain.service.UsmRiskAssessmentStationService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmRiskAssessmentStation")
@Api(tags = "泵站风险评估表接口")
public class UsmRiskAssessmentStationController extends BaseController<UsmRiskAssessmentStationService, UsmRiskAssessmentStation, UsmRiskAssessmentStationForm, UsmRiskAssessmentStationDto, String> {

    private final UsmRiskAssessmentStationService usmRiskAssessmentStationService;

    @Autowired
    public UsmRiskAssessmentStationController(UsmRiskAssessmentStationService service) {
        super(service);
        this.usmRiskAssessmentStationService = service;
    }
}