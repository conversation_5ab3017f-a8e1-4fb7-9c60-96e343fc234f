package com.seali.drain.controller;

import com.seali.drain.pojo.UsmBasicPumpStation;
import com.seali.drain.dto.UsmBasicPumpStationDto;
import com.seali.drain.form.UsmBasicPumpStationForm;
import com.seali.drain.service.UsmBasicPumpStationService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmBasicPumpStation")
@Api(tags = "泵站信息表接口")
public class UsmBasicPumpStationController extends BaseController<UsmBasicPumpStationService, UsmBasicPumpStation, UsmBasicPumpStationForm, UsmBasicPumpStationDto, String> {

    private final UsmBasicPumpStationService usmBasicPumpStationService;

    @Autowired
    public UsmBasicPumpStationController(UsmBasicPumpStationService service) {
        super(service);
        this.usmBasicPumpStationService = service;
    }
}