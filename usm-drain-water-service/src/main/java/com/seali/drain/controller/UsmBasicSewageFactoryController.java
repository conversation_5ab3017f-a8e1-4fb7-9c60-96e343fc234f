package com.seali.drain.controller;

import com.seali.drain.pojo.UsmBasicSewageFactory;
import com.seali.drain.dto.UsmBasicSewageFactoryDto;
import com.seali.drain.form.UsmBasicSewageFactoryForm;
import com.seali.drain.service.UsmBasicSewageFactoryService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmBasicSewageFactory")
@Api(tags = "污水厂信息表接口")
public class UsmBasicSewageFactoryController extends BaseController<UsmBasicSewageFactoryService, UsmBasicSewageFactory, UsmBasicSewageFactoryForm, UsmBasicSewageFactoryDto, String> {

    private final UsmBasicSewageFactoryService usmBasicSewageFactoryService;

    @Autowired
    public UsmBasicSewageFactoryController(UsmBasicSewageFactoryService service) {
        super(service);
        this.usmBasicSewageFactoryService = service;
    }
}