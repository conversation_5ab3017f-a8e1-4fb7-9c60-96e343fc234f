package com.seali.drain.controller;

import com.seali.drain.pojo.UsmMonitorAlarm;
import com.seali.drain.dto.UsmMonitorAlarmDto;
import com.seali.drain.form.UsmMonitorAlarmForm;
import com.seali.drain.service.UsmMonitorAlarmService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmMonitorAlarm")
@Api(tags = "设备报警表接口")
public class UsmMonitorAlarmController extends BaseController<UsmMonitorAlarmService, UsmMonitorAlarm, UsmMonitorAlarmForm, UsmMonitorAlarmDto, String> {

    private final UsmMonitorAlarmService usmMonitorAlarmService;

    @Autowired
    public UsmMonitorAlarmController(UsmMonitorAlarmService service) {
        super(service);
        this.usmMonitorAlarmService = service;
    }
}