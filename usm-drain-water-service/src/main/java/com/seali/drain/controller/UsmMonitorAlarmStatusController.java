package com.seali.drain.controller;

import com.seali.drain.pojo.UsmMonitorAlarmStatus;
import com.seali.drain.dto.UsmMonitorAlarmStatusDto;
import com.seali.drain.form.UsmMonitorAlarmStatusForm;
import com.seali.drain.service.UsmMonitorAlarmStatusService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmMonitorAlarmStatus")
@Api(tags = "报警状态表接口")
public class UsmMonitorAlarmStatusController extends BaseController<UsmMonitorAlarmStatusService, UsmMonitorAlarmStatus, UsmMonitorAlarmStatusForm, UsmMonitorAlarmStatusDto, String> {

    private final UsmMonitorAlarmStatusService usmMonitorAlarmStatusService;

    @Autowired
    public UsmMonitorAlarmStatusController(UsmMonitorAlarmStatusService service) {
        super(service);
        this.usmMonitorAlarmStatusService = service;
    }
}