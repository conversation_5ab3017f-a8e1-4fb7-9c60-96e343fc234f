package com.seali.drain.controller;

import com.seali.drain.pojo.UsmAlarmThreshold;
import com.seali.drain.dto.UsmAlarmThresholdDto;
import com.seali.drain.form.UsmAlarmThresholdForm;
import com.seali.drain.service.UsmAlarmThresholdService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmAlarmThreshold")
@Api(tags = "报警阈值配置表接口")
public class UsmAlarmThresholdController extends BaseController<UsmAlarmThresholdService, UsmAlarmThreshold, UsmAlarmThresholdForm, UsmAlarmThresholdDto, String> {

    private final UsmAlarmThresholdService usmAlarmThresholdService;

    @Autowired
    public UsmAlarmThresholdController(UsmAlarmThresholdService service) {
        super(service);
        this.usmAlarmThresholdService = service;
    }
}