package com.seali.drain.controller;

import com.seali.drain.pojo.UsmBasicDrainOutlet;
import com.seali.drain.dto.UsmBasicDrainOutletDto;
import com.seali.drain.form.UsmBasicDrainOutletForm;
import com.seali.drain.service.UsmBasicDrainOutletService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmBasicDrainOutlet")
@Api(tags = "排水口信息表接口")
public class UsmBasicDrainOutletController extends BaseController<UsmBasicDrainOutletService, UsmBasicDrainOutlet, UsmBasicDrainOutletForm, UsmBasicDrainOutletDto, String> {

    private final UsmBasicDrainOutletService usmBasicDrainOutletService;

    @Autowired
    public UsmBasicDrainOutletController(UsmBasicDrainOutletService service) {
        super(service);
        this.usmBasicDrainOutletService = service;
    }
}