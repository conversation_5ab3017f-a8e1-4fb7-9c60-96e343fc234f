package com.seali.drain.controller;

import com.seali.drain.pojo.UsmRiskAssessmentPipeline;
import com.seali.drain.dto.UsmRiskAssessmentPipelineDto;
import com.seali.drain.form.UsmRiskAssessmentPipelineForm;
import com.seali.drain.service.UsmRiskAssessmentPipelineService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmRiskAssessmentPipeline")
@Api(tags = "管网风险评估表接口")
public class UsmRiskAssessmentPipelineController extends BaseController<UsmRiskAssessmentPipelineService, UsmRiskAssessmentPipeline, UsmRiskAssessmentPipelineForm, UsmRiskAssessmentPipelineDto, String> {

    private final UsmRiskAssessmentPipelineService usmRiskAssessmentPipelineService;

    @Autowired
    public UsmRiskAssessmentPipelineController(UsmRiskAssessmentPipelineService service) {
        super(service);
        this.usmRiskAssessmentPipelineService = service;
    }
}