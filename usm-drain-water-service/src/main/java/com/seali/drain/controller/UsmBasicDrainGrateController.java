package com.seali.drain.controller;

import com.seali.drain.pojo.UsmBasicDrainGrate;
import com.seali.drain.dto.UsmBasicDrainGrateDto;
import com.seali.drain.form.UsmBasicDrainGrateForm;
import com.seali.drain.service.UsmBasicDrainGrateService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmBasicDrainGrate")
@Api(tags = "雨水篦子信息表接口")
public class UsmBasicDrainGrateController extends BaseController<UsmBasicDrainGrateService, UsmBasicDrainGrate, UsmBasicDrainGrateForm, UsmBasicDrainGrateDto, String> {

    private final UsmBasicDrainGrateService usmBasicDrainGrateService;

    @Autowired
    public UsmBasicDrainGrateController(UsmBasicDrainGrateService service) {
        super(service);
        this.usmBasicDrainGrateService = service;
    }
}