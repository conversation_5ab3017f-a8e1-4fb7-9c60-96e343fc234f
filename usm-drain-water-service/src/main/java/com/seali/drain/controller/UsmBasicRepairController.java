package com.seali.drain.controller;

import com.seali.drain.pojo.UsmBasicRepair;
import com.seali.drain.dto.UsmBasicRepairDto;
import com.seali.drain.form.UsmBasicRepairForm;
import com.seali.drain.service.UsmBasicRepairService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmBasicRepair")
@Api(tags = "管线维修记录表接口")
public class UsmBasicRepairController extends BaseController<UsmBasicRepairService, UsmBasicRepair, UsmBasicRepairForm, UsmBasicRepairDto, String> {

    private final UsmBasicRepairService usmBasicRepairService;

    @Autowired
    public UsmBasicRepairController(UsmBasicRepairService service) {
        super(service);
        this.usmBasicRepairService = service;
    }
}