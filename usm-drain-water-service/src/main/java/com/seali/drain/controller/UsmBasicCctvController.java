package com.seali.drain.controller;

import com.seali.drain.pojo.UsmBasicCctv;
import com.seali.drain.dto.UsmBasicCctvDto;
import com.seali.drain.form.UsmBasicCctvForm;
import com.seali.drain.service.UsmBasicCctvService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmBasicCctv")
@Api(tags = "CCTV检测信息表接口")
public class UsmBasicCctvController extends BaseController<UsmBasicCctvService, UsmBasicCctv, UsmBasicCctvForm, UsmBasicCctvDto, String> {

    private final UsmBasicCctvService usmBasicCctvService;

    @Autowired
    public UsmBasicCctvController(UsmBasicCctvService service) {
        super(service);
        this.usmBasicCctvService = service;
    }
}