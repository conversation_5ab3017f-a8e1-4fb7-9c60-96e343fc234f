package com.seali.drain.controller;

import com.seali.drain.pojo.UsmMonitorDevice;
import com.seali.drain.dto.UsmMonitorDeviceDto;
import com.seali.drain.form.UsmMonitorDeviceForm;
import com.seali.drain.service.UsmMonitorDeviceService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmMonitorDevice")
@Api(tags = "排水设备信息接口")
public class UsmMonitorDeviceController extends BaseController<UsmMonitorDeviceService, UsmMonitorDevice, UsmMonitorDeviceForm, UsmMonitorDeviceDto, String> {

    private final UsmMonitorDeviceService usmMonitorDeviceService;

    @Autowired
    public UsmMonitorDeviceController(UsmMonitorDeviceService service) {
        super(service);
        this.usmMonitorDeviceService = service;
    }
}