package com.seali.drain.controller;

import com.seali.drain.pojo.UsmBasicWell;
import com.seali.drain.dto.UsmBasicWellDto;
import com.seali.drain.form.UsmBasicWellForm;
import com.seali.drain.service.UsmBasicWellService;
import com.seali.common.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usmBasicWell")
@Api(tags = "排水窨井信息表接口")
public class UsmBasicWellController extends BaseController<UsmBasicWellService, UsmBasicWell, UsmBasicWellForm, UsmBasicWellDto, String> {

    private final UsmBasicWellService usmBasicWellService;

    @Autowired
    public UsmBasicWellController(UsmBasicWellService service) {
        super(service);
        this.usmBasicWellService = service;
    }
}