package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmAlarmThreshold;
import com.seali.drain.dto.UsmAlarmThresholdDto;
import com.seali.drain.form.UsmAlarmThresholdForm;
import com.seali.drain.dao.UsmAlarmThresholdMapper;
import com.seali.drain.service.UsmAlarmThresholdService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmAlarmThresholdServiceImpl extends BaseServiceImpl<UsmAlarmThresholdMapper, UsmAlarmThreshold, UsmAlarmThresholdForm, UsmAlarmThresholdDto, String> implements UsmAlarmThresholdService {

    @Override
    protected UsmAlarmThreshold formToEntity(UsmAlarmThresholdForm form) {
        UsmAlarmThreshold entity = new UsmAlarmThreshold();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmAlarmThresholdDto entityToDto(UsmAlarmThreshold entity) {
        UsmAlarmThresholdDto dto = new UsmAlarmThresholdDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}