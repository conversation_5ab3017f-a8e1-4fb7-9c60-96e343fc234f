package com.seali.drain.service;

import com.seali.drain.pojo.UsmRiskAssessmentStation;
import com.seali.drain.dto.UsmRiskAssessmentStationDto;
import com.seali.drain.form.UsmRiskAssessmentStationForm;
import com.seali.common.service.BaseService;

public interface UsmRiskAssessmentStationService extends BaseService<UsmRiskAssessmentStation, UsmRiskAssessmentStationForm, UsmRiskAssessmentStationDto, String> {

}