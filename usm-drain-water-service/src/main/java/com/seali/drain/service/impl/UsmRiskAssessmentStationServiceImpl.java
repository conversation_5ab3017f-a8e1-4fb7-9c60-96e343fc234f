package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmRiskAssessmentStation;
import com.seali.drain.dto.UsmRiskAssessmentStationDto;
import com.seali.drain.form.UsmRiskAssessmentStationForm;
import com.seali.drain.dao.UsmRiskAssessmentStationMapper;
import com.seali.drain.service.UsmRiskAssessmentStationService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmRiskAssessmentStationServiceImpl extends BaseServiceImpl<UsmRiskAssessmentStationMapper, UsmRiskAssessmentStation, UsmRiskAssessmentStationForm, UsmRiskAssessmentStationDto, String> implements UsmRiskAssessmentStationService {

    @Override
    protected UsmRiskAssessmentStation formToEntity(UsmRiskAssessmentStationForm form) {
        UsmRiskAssessmentStation entity = new UsmRiskAssessmentStation();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmRiskAssessmentStationDto entityToDto(UsmRiskAssessmentStation entity) {
        UsmRiskAssessmentStationDto dto = new UsmRiskAssessmentStationDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}