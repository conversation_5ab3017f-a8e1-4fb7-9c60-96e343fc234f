package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmRiskAssessmentPipeline;
import com.seali.drain.dto.UsmRiskAssessmentPipelineDto;
import com.seali.drain.form.UsmRiskAssessmentPipelineForm;
import com.seali.drain.dao.UsmRiskAssessmentPipelineMapper;
import com.seali.drain.service.UsmRiskAssessmentPipelineService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmRiskAssessmentPipelineServiceImpl extends BaseServiceImpl<UsmRiskAssessmentPipelineMapper, UsmRiskAssessmentPipeline, UsmRiskAssessmentPipelineForm, UsmRiskAssessmentPipelineDto, String> implements UsmRiskAssessmentPipelineService {

    @Override
    protected UsmRiskAssessmentPipeline formToEntity(UsmRiskAssessmentPipelineForm form) {
        UsmRiskAssessmentPipeline entity = new UsmRiskAssessmentPipeline();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmRiskAssessmentPipelineDto entityToDto(UsmRiskAssessmentPipeline entity) {
        UsmRiskAssessmentPipelineDto dto = new UsmRiskAssessmentPipelineDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}