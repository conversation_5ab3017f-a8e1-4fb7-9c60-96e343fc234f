package com.seali.drain.service;

import com.seali.drain.pojo.UsmRiskAssessmentFactory;
import com.seali.drain.dto.UsmRiskAssessmentFactoryDto;
import com.seali.drain.form.UsmRiskAssessmentFactoryForm;
import com.seali.common.service.BaseService;

public interface UsmRiskAssessmentFactoryService extends BaseService<UsmRiskAssessmentFactory, UsmRiskAssessmentFactoryForm, UsmRiskAssessmentFactoryDto, String> {

}