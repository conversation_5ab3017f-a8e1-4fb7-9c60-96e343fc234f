package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmMonitorAlarm;
import com.seali.drain.dto.UsmMonitorAlarmDto;
import com.seali.drain.form.UsmMonitorAlarmForm;
import com.seali.drain.dao.UsmMonitorAlarmMapper;
import com.seali.drain.service.UsmMonitorAlarmService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmMonitorAlarmServiceImpl extends BaseServiceImpl<UsmMonitorAlarmMapper, UsmMonitorAlarm, UsmMonitorAlarmForm, UsmMonitorAlarmDto, String> implements UsmMonitorAlarmService {

    @Override
    protected UsmMonitorAlarm formToEntity(UsmMonitorAlarmForm form) {
        UsmMonitorAlarm entity = new UsmMonitorAlarm();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmMonitorAlarmDto entityToDto(UsmMonitorAlarm entity) {
        UsmMonitorAlarmDto dto = new UsmMonitorAlarmDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}