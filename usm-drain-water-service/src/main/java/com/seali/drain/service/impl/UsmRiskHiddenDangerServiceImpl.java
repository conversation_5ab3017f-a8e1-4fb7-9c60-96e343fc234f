package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmRiskHiddenDanger;
import com.seali.drain.dto.UsmRiskHiddenDangerDto;
import com.seali.drain.form.UsmRiskHiddenDangerForm;
import com.seali.drain.dao.UsmRiskHiddenDangerMapper;
import com.seali.drain.service.UsmRiskHiddenDangerService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmRiskHiddenDangerServiceImpl extends BaseServiceImpl<UsmRiskHiddenDangerMapper, UsmRiskHiddenDanger, UsmRiskHiddenDangerForm, UsmRiskHiddenDangerDto, String> implements UsmRiskHiddenDangerService {

    @Override
    protected UsmRiskHiddenDanger formToEntity(UsmRiskHiddenDangerForm form) {
        UsmRiskHiddenDanger entity = new UsmRiskHiddenDanger();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmRiskHiddenDangerDto entityToDto(UsmRiskHiddenDanger entity) {
        UsmRiskHiddenDangerDto dto = new UsmRiskHiddenDangerDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}