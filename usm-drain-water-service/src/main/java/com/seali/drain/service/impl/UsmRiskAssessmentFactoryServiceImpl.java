package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmRiskAssessmentFactory;
import com.seali.drain.dto.UsmRiskAssessmentFactoryDto;
import com.seali.drain.form.UsmRiskAssessmentFactoryForm;
import com.seali.drain.dao.UsmRiskAssessmentFactoryMapper;
import com.seali.drain.service.UsmRiskAssessmentFactoryService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmRiskAssessmentFactoryServiceImpl extends BaseServiceImpl<UsmRiskAssessmentFactoryMapper, UsmRiskAssessmentFactory, UsmRiskAssessmentFactoryForm, UsmRiskAssessmentFactoryDto, String> implements UsmRiskAssessmentFactoryService {

    @Override
    protected UsmRiskAssessmentFactory formToEntity(UsmRiskAssessmentFactoryForm form) {
        UsmRiskAssessmentFactory entity = new UsmRiskAssessmentFactory();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmRiskAssessmentFactoryDto entityToDto(UsmRiskAssessmentFactory entity) {
        UsmRiskAssessmentFactoryDto dto = new UsmRiskAssessmentFactoryDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}