package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmMonitorDevice;
import com.seali.drain.dto.UsmMonitorDeviceDto;
import com.seali.drain.form.UsmMonitorDeviceForm;
import com.seali.drain.dao.UsmMonitorDeviceMapper;
import com.seali.drain.service.UsmMonitorDeviceService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmMonitorDeviceServiceImpl extends BaseServiceImpl<UsmMonitorDeviceMapper, UsmMonitorDevice, UsmMonitorDeviceForm, UsmMonitorDeviceDto, String> implements UsmMonitorDeviceService {

    @Override
    protected UsmMonitorDevice formToEntity(UsmMonitorDeviceForm form) {
        UsmMonitorDevice entity = new UsmMonitorDevice();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmMonitorDeviceDto entityToDto(UsmMonitorDevice entity) {
        UsmMonitorDeviceDto dto = new UsmMonitorDeviceDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}