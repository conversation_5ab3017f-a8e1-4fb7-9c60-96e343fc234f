package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmBasicDrainOutlet;
import com.seali.drain.dto.UsmBasicDrainOutletDto;
import com.seali.drain.form.UsmBasicDrainOutletForm;
import com.seali.drain.dao.UsmBasicDrainOutletMapper;
import com.seali.drain.service.UsmBasicDrainOutletService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmBasicDrainOutletServiceImpl extends BaseServiceImpl<UsmBasicDrainOutletMapper, UsmBasicDrainOutlet, UsmBasicDrainOutletForm, UsmBasicDrainOutletDto, String> implements UsmBasicDrainOutletService {

    @Override
    protected UsmBasicDrainOutlet formToEntity(UsmBasicDrainOutletForm form) {
        UsmBasicDrainOutlet entity = new UsmBasicDrainOutlet();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmBasicDrainOutletDto entityToDto(UsmBasicDrainOutlet entity) {
        UsmBasicDrainOutletDto dto = new UsmBasicDrainOutletDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}