package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmBasicSewageFactory;
import com.seali.drain.dto.UsmBasicSewageFactoryDto;
import com.seali.drain.form.UsmBasicSewageFactoryForm;
import com.seali.drain.dao.UsmBasicSewageFactoryMapper;
import com.seali.drain.service.UsmBasicSewageFactoryService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmBasicSewageFactoryServiceImpl extends BaseServiceImpl<UsmBasicSewageFactoryMapper, UsmBasicSewageFactory, UsmBasicSewageFactoryForm, UsmBasicSewageFactoryDto, String> implements UsmBasicSewageFactoryService {

    @Override
    protected UsmBasicSewageFactory formToEntity(UsmBasicSewageFactoryForm form) {
        UsmBasicSewageFactory entity = new UsmBasicSewageFactory();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmBasicSewageFactoryDto entityToDto(UsmBasicSewageFactory entity) {
        UsmBasicSewageFactoryDto dto = new UsmBasicSewageFactoryDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}