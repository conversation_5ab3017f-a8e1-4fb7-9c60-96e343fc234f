package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmBasicRepair;
import com.seali.drain.dto.UsmBasicRepairDto;
import com.seali.drain.form.UsmBasicRepairForm;
import com.seali.drain.dao.UsmBasicRepairMapper;
import com.seali.drain.service.UsmBasicRepairService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmBasicRepairServiceImpl extends BaseServiceImpl<UsmBasicRepairMapper, UsmBasicRepair, UsmBasicRepairForm, UsmBasicRepairDto, String> implements UsmBasicRepairService {

    @Override
    protected UsmBasicRepair formToEntity(UsmBasicRepairForm form) {
        UsmBasicRepair entity = new UsmBasicRepair();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmBasicRepairDto entityToDto(UsmBasicRepair entity) {
        UsmBasicRepairDto dto = new UsmBasicRepairDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}