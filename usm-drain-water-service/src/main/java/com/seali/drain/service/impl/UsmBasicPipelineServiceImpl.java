package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmBasicPipeline;
import com.seali.drain.dto.UsmBasicPipelineDto;
import com.seali.drain.form.UsmBasicPipelineForm;
import com.seali.drain.dao.UsmBasicPipelineMapper;
import com.seali.drain.service.UsmBasicPipelineService;
import com.seali.common.service.impl.BaseServiceImpl;
import com.seali.drain.util.GeometryUtils;
import org.postgis.Geometry;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class UsmBasicPipelineServiceImpl extends BaseServiceImpl<UsmBasicPipelineMapper, UsmBasicPipeline, UsmBasicPipelineForm, UsmBasicPipelineDto, String> implements UsmBasicPipelineService {

    @Override
    protected UsmBasicPipeline formToEntity(UsmBasicPipelineForm form) {
        UsmBasicPipeline entity = new UsmBasicPipeline();
        BeanUtils.copyProperties(form, entity);

        // 处理几何数据
        if (!StringUtils.isEmpty(form.getGeomText())) {
            // 将 WKT 格式的几何数据转换为 PostGIS Geometry 对象
            entity.setGeom(GeometryUtils.wktToPostgisGeometry(form.getGeomText()));
        } else if (form.getStartPointLongitude() != null && form.getStartPointLatitude() != null &&
                form.getEndPointLongitude() != null && form.getEndPointLatitude() != null) {
            // 如果没有 geomText 但有起点和终点的经纬度，则创建一条线段
            String wkt = String.format("LINESTRING(%s %s, %s %s)",
                    form.getStartPointLongitude(), form.getStartPointLatitude(),
                    form.getEndPointLongitude(), form.getEndPointLatitude());
            entity.setGeom(GeometryUtils.wktToPostgisGeometry(wkt));
        }

        return entity;
    }

    @Override
    protected UsmBasicPipelineDto entityToDto(UsmBasicPipeline entity) {
        UsmBasicPipelineDto dto = new UsmBasicPipelineDto();
        BeanUtils.copyProperties(entity, dto);

        // 处理几何数据
        Geometry geom = entity.getGeom();
        if (geom != null) {
            dto.setGeomText(GeometryUtils.postgisGeometryToWkt(geom));
        } else if (entity.getStartPointLongitude() != null && entity.getStartPointLatitude() != null &&
                entity.getEndPointLongitude() != null && entity.getEndPointLatitude() != null) {
            // 如果没有 geom 字段但有起点和终点的经纬度，则构造线段的 WKT 格式文本
            dto.setGeomText(String.format("LINESTRING(%s %s, %s %s)",
                    entity.getStartPointLongitude(), entity.getStartPointLatitude(),
                    entity.getEndPointLongitude(), entity.getEndPointLatitude()));
        }

        return dto;
    }
}