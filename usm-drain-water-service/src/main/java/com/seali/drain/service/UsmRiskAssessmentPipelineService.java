package com.seali.drain.service;

import com.seali.drain.pojo.UsmRiskAssessmentPipeline;
import com.seali.drain.dto.UsmRiskAssessmentPipelineDto;
import com.seali.drain.form.UsmRiskAssessmentPipelineForm;
import com.seali.common.service.BaseService;

public interface UsmRiskAssessmentPipelineService extends BaseService<UsmRiskAssessmentPipeline, UsmRiskAssessmentPipelineForm, UsmRiskAssessmentPipelineDto, String> {

}