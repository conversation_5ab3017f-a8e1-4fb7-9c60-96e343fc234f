package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmBasicFloodPoint;
import com.seali.drain.dto.UsmBasicFloodPointDto;
import com.seali.drain.form.UsmBasicFloodPointForm;
import com.seali.drain.dao.UsmBasicFloodPointMapper;
import com.seali.drain.service.UsmBasicFloodPointService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmBasicFloodPointServiceImpl extends BaseServiceImpl<UsmBasicFloodPointMapper, UsmBasicFloodPoint, UsmBasicFloodPointForm, UsmBasicFloodPointDto, String> implements UsmBasicFloodPointService {

    @Override
    protected UsmBasicFloodPoint formToEntity(UsmBasicFloodPointForm form) {
        UsmBasicFloodPoint entity = new UsmBasicFloodPoint();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmBasicFloodPointDto entityToDto(UsmBasicFloodPoint entity) {
        UsmBasicFloodPointDto dto = new UsmBasicFloodPointDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}