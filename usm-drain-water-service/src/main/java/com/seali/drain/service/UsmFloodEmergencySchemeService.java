package com.seali.drain.service;

import com.seali.drain.pojo.UsmFloodEmergencyScheme;
import com.seali.drain.dto.UsmFloodEmergencySchemeDto;
import com.seali.drain.form.UsmFloodEmergencySchemeForm;
import com.seali.common.service.BaseService;

public interface UsmFloodEmergencySchemeService extends BaseService<UsmFloodEmergencyScheme, UsmFloodEmergencySchemeForm, UsmFloodEmergencySchemeDto, String> {

}