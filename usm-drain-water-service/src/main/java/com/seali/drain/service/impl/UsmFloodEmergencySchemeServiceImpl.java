package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmFloodEmergencyScheme;
import com.seali.drain.dto.UsmFloodEmergencySchemeDto;
import com.seali.drain.form.UsmFloodEmergencySchemeForm;
import com.seali.drain.dao.UsmFloodEmergencySchemeMapper;
import com.seali.drain.service.UsmFloodEmergencySchemeService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmFloodEmergencySchemeServiceImpl extends BaseServiceImpl<UsmFloodEmergencySchemeMapper, UsmFloodEmergencyScheme, UsmFloodEmergencySchemeForm, UsmFloodEmergencySchemeDto, String> implements UsmFloodEmergencySchemeService {

    @Override
    protected UsmFloodEmergencyScheme formToEntity(UsmFloodEmergencySchemeForm form) {
        UsmFloodEmergencyScheme entity = new UsmFloodEmergencyScheme();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmFloodEmergencySchemeDto entityToDto(UsmFloodEmergencyScheme entity) {
        UsmFloodEmergencySchemeDto dto = new UsmFloodEmergencySchemeDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}