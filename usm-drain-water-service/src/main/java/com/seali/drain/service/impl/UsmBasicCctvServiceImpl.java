package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmBasicCctv;
import com.seali.drain.dto.UsmBasicCctvDto;
import com.seali.drain.form.UsmBasicCctvForm;
import com.seali.drain.dao.UsmBasicCctvMapper;
import com.seali.drain.service.UsmBasicCctvService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmBasicCctvServiceImpl extends BaseServiceImpl<UsmBasicCctvMapper, UsmBasicCctv, UsmBasicCctvForm, UsmBasicCctvDto, String> implements UsmBasicCctvService {

    @Override
    protected UsmBasicCctv formToEntity(UsmBasicCctvForm form) {
        UsmBasicCctv entity = new UsmBasicCctv();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmBasicCctvDto entityToDto(UsmBasicCctv entity) {
        UsmBasicCctvDto dto = new UsmBasicCctvDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}