package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmBasicPumpStation;
import com.seali.drain.dto.UsmBasicPumpStationDto;
import com.seali.drain.form.UsmBasicPumpStationForm;
import com.seali.drain.dao.UsmBasicPumpStationMapper;
import com.seali.drain.service.UsmBasicPumpStationService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmBasicPumpStationServiceImpl extends BaseServiceImpl<UsmBasicPumpStationMapper, UsmBasicPumpStation, UsmBasicPumpStationForm, UsmBasicPumpStationDto, String> implements UsmBasicPumpStationService {

    @Override
    protected UsmBasicPumpStation formToEntity(UsmBasicPumpStationForm form) {
        UsmBasicPumpStation entity = new UsmBasicPumpStation();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmBasicPumpStationDto entityToDto(UsmBasicPumpStation entity) {
        UsmBasicPumpStationDto dto = new UsmBasicPumpStationDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}