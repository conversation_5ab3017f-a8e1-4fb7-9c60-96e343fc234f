package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmBasicWell;
import com.seali.drain.dto.UsmBasicWellDto;
import com.seali.drain.form.UsmBasicWellForm;
import com.seali.drain.dao.UsmBasicWellMapper;
import com.seali.drain.service.UsmBasicWellService;
import com.seali.common.service.impl.BaseServiceImpl;
import com.seali.drain.util.GeometryUtils;
import org.postgis.Geometry;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class UsmBasicWellServiceImpl extends BaseServiceImpl<UsmBasicWellMapper, UsmBasicWell, UsmBasicWellForm, UsmBasicWellDto, String> implements UsmBasicWellService {

    @Override
    protected UsmBasicWell formToEntity(UsmBasicWellForm form) {
        UsmBasicWell entity = new UsmBasicWell();
        BeanUtils.copyProperties(form, entity);

        // 处理几何数据
        if (!StringUtils.isEmpty(form.getGeomText())) {
            // 将 WKT 格式的几何数据转换为 PostGIS Geometry 对象
            entity.setGeom(GeometryUtils.wktToPostgisGeometry(form.getGeomText()));
        } else if (form.getLongitude() != null && form.getLatitude() != null) {
            // 如果没有 geomText 但有经纬度，则根据经纬度创建 PostGIS Geometry 对象
            String wkt = GeometryUtils.createWktPoint(form.getLongitude().doubleValue(), form.getLatitude().doubleValue());
            entity.setGeom(GeometryUtils.wktToPostgisGeometry(wkt));
        }

        return entity;
    }

    @Override
    protected UsmBasicWellDto entityToDto(UsmBasicWell entity) {
        UsmBasicWellDto dto = new UsmBasicWellDto();
        BeanUtils.copyProperties(entity, dto);

        // 处理几何数据
        Geometry geom = entity.getGeom();
        if (geom != null) {
            dto.setGeomText(GeometryUtils.postgisGeometryToWkt(geom));
        } else if (entity.getLongitude() != null && entity.getLatitude() != null) {
            // 如果没有 geom 字段但有经纬度，则构造纯粹的 WKT 格式文本
            dto.setGeomText(GeometryUtils.createWktPoint(entity.getLongitude().doubleValue(), entity.getLatitude().doubleValue()));
        }

        return dto;
    }
}