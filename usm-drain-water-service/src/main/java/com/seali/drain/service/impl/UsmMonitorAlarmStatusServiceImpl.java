package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmMonitorAlarmStatus;
import com.seali.drain.dto.UsmMonitorAlarmStatusDto;
import com.seali.drain.form.UsmMonitorAlarmStatusForm;
import com.seali.drain.dao.UsmMonitorAlarmStatusMapper;
import com.seali.drain.service.UsmMonitorAlarmStatusService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmMonitorAlarmStatusServiceImpl extends BaseServiceImpl<UsmMonitorAlarmStatusMapper, UsmMonitorAlarmStatus, UsmMonitorAlarmStatusForm, UsmMonitorAlarmStatusDto, String> implements UsmMonitorAlarmStatusService {

    @Override
    protected UsmMonitorAlarmStatus formToEntity(UsmMonitorAlarmStatusForm form) {
        UsmMonitorAlarmStatus entity = new UsmMonitorAlarmStatus();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmMonitorAlarmStatusDto entityToDto(UsmMonitorAlarmStatus entity) {
        UsmMonitorAlarmStatusDto dto = new UsmMonitorAlarmStatusDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}