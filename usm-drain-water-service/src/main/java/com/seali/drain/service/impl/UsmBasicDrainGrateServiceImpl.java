package com.seali.drain.service.impl;

import com.seali.drain.pojo.UsmBasicDrainGrate;
import com.seali.drain.dto.UsmBasicDrainGrateDto;
import com.seali.drain.form.UsmBasicDrainGrateForm;
import com.seali.drain.dao.UsmBasicDrainGrateMapper;
import com.seali.drain.service.UsmBasicDrainGrateService;
import com.seali.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class UsmBasicDrainGrateServiceImpl extends BaseServiceImpl<UsmBasicDrainGrateMapper, UsmBasicDrainGrate, UsmBasicDrainGrateForm, UsmBasicDrainGrateDto, String> implements UsmBasicDrainGrateService {

    @Override
    protected UsmBasicDrainGrate formToEntity(UsmBasicDrainGrateForm form) {
        UsmBasicDrainGrate entity = new UsmBasicDrainGrate();
        BeanUtils.copyProperties(form, entity);
        return entity;
    }

    @Override
    protected UsmBasicDrainGrateDto entityToDto(UsmBasicDrainGrate entity) {
        UsmBasicDrainGrateDto dto = new UsmBasicDrainGrateDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}