package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmRiskEvent对象", description = "排水应急事件信息表")
@TableName(value = "usm_risk_event", autoResultMap = true)
public class UsmRiskEvent extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("事件来源")
    @TableField("event_source_type")
    private String eventSourceType;

    @ApiModelProperty("事件来源名称")
    @TableField("event_source_name")
    private String eventSourceName;

    @ApiModelProperty("事件标题")
    @TableField("event_title")
    private String eventTitle;

    @ApiModelProperty("事件描述")
    @TableField("event_desc")
    private String eventDesc;

    @ApiModelProperty("事件分类（3003201：城市内涝）")
    @TableField("event_type")
    private Integer eventType;

    @ApiModelProperty("事件分类名称")
    @TableField("event_type_name")
    private String eventTypeName;

    @ApiModelProperty("事件分级（3003301：特别重大，3003302：重大，3003303：较大，3003304：一般）")
    @TableField("event_level")
    private Integer eventLevel;

    @ApiModelProperty("事件分级名称")
    @TableField("event_level_name")
    private String eventLevelName;

    @ApiModelProperty("发生时间")
    @TableField("event_time")
    private Timestamp eventTime;

    @ApiModelProperty("是否人员伤亡（0：否，1：是）")
    @TableField("is_casualty")
    private String isCasualty;

    @ApiModelProperty("死亡人数")
    @TableField("death_num")
    private Integer deathNum;

    @ApiModelProperty("受伤人数")
    @TableField("injured_num")
    private Integer injuredNum;

    @ApiModelProperty("上报人员联系方式")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty("责任单位code")
    @TableField("ownership_unit")
    private String ownershipUnit;

    @ApiModelProperty("责任单位名称")
    @TableField("ownership_unit_name")
    private String ownershipUnitName;

    @ApiModelProperty("接收时间")
    @TableField("receive_time")
    private Timestamp receiveTime;

    @ApiModelProperty("事件处置状态")
    @TableField("event_status")
    private Integer eventStatus;

    @ApiModelProperty("事件处置状态名称")
    @TableField("event_status_name")
    private String eventStatusName;

    @ApiModelProperty("处置完成时间")
    @TableField("handle_time")
    private Timestamp handleTime;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}