package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicDrainOutlet对象", description = "排水口信息表")
@TableName(value = "usm_basic_drain_outlet", autoResultMap = true)
public class UsmBasicDrainOutlet extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("排口名称")
    @TableField("outlet_name")
    private String outletName;

    @ApiModelProperty("排口类型（3000801：污水排水，3000802：雨水排水，3000803：合流排水，3000804：工业废水）")
    @TableField("outlet_type")
    private Integer outletType;

    @ApiModelProperty("排口类型名称")
    @TableField("outlet_type_name")
    private String outletTypeName;

    @ApiModelProperty("地表高程（m）")
    @TableField("floor_distance")
    private BigDecimal floorDistance;

    @ApiModelProperty("顶部高程（m）")
    @TableField("top_distance")
    private BigDecimal topDistance;

    @ApiModelProperty("排口尺寸（mm）")
    @TableField("outlet_size")
    private String outletSize;

    @ApiModelProperty("出流形式")
    @TableField("flow_type")
    private String flowType;

    @ApiModelProperty("是否有拍门（0：否，1：是）")
    @TableField("is_flap_gate")
    private String isFlapGate;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}