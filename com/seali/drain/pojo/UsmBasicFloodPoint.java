package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicFloodPoint对象", description = "易涝点信息表")
@TableName(value = "usm_basic_flood_point", autoResultMap = true)
public class UsmBasicFloodPoint extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("易涝点名称")
    @TableField("point_name")
    private String pointName;

    @ApiModelProperty("易涝点编码")
    @TableField("point_code")
    private String pointCode;

    @ApiModelProperty("易涝点风险（3001801：重大风险，3001802：较大风险，3001803：一般风险，3001804：低风险）")
    @TableField("risk_level")
    private Integer riskLevel;

    @ApiModelProperty("易涝点风险名称")
    @TableField("risk_level_name")
    private String riskLevelName;

    @ApiModelProperty("所在道路")
    @TableField("road_name")
    private String roadName;

    @ApiModelProperty("最长积水时间（h）")
    @TableField("max_hydrops_time")
    private String maxHydropsTime;

    @ApiModelProperty("最大积水面积（m²）")
    @TableField("max_hydrops_area")
    private String maxHydropsArea;

    @ApiModelProperty("积水原因")
    @TableField("hydrops_reason")
    private String hydropsReason;

    @ApiModelProperty("积易涝点产生时间")
    @TableField("happen_time")
    private Timestamp happenTime;

    @ApiModelProperty("负责人")
    @TableField("contact_user")
    private String contactUser;

    @ApiModelProperty("联系方式")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty("整改状态（3001901：已整改，3001902：未整改）")
    @TableField("rectification_status")
    private Integer rectificationStatus;

    @ApiModelProperty("整改状态名称")
    @TableField("rectification_status_name")
    private String rectificationStatusName;

    @ApiModelProperty("整改时间")
    @TableField("rectification_time")
    private Timestamp rectificationTime;

    @ApiModelProperty("整改人")
    @TableField("rectification_user")
    private String rectificationUser;

    @ApiModelProperty("整改措施")
    @TableField("rectification_measure")
    private String rectificationMeasure;

    @ApiModelProperty("附件地址")
    @TableField("attachment_urls")
    private String attachmentUrls;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}