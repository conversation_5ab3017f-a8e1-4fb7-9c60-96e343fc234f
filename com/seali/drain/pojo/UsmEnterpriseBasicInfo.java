package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmEnterpriseBasicInfo对象", description = "企业基本信息表")
@TableName(value = "usm_enterprise_basic_info", autoResultMap = true)
public class UsmEnterpriseBasicInfo extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("企业名称")
    @TableField("enterprise_name")
    private String enterpriseName;

    @ApiModelProperty("企业统一信用代码")
    @TableField("unified_social_credit_code")
    private String unifiedSocialCreditCode;

    @ApiModelProperty("企业联系人")
    @TableField("contact_person")
    private String contactPerson;

    @ApiModelProperty("联系方式")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty("企业地址")
    @TableField("enterprise_address")
    private String enterpriseAddress;

    @ApiModelProperty("服务用户数")
    @TableField("service_user_count")
    private Integer serviceUserCount;

    @ApiModelProperty("管网公里数")
    @TableField("pipeline_length_km")
    private BigDecimal pipelineLengthKm;

    @ApiModelProperty("企业简介")
    @TableField("enterprise_introduction")
    private String enterpriseIntroduction;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

}