package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmKnowledgeBase对象", description = "知识库信息表")
@TableName(value = "usm_knowledge_base", autoResultMap = true)
public class UsmKnowledgeBase extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("知识库名称")
    @TableField("knowledge_name")
    private String knowledgeName;

    @ApiModelProperty("知识库类型")
    @TableField("knowledge_type")
    private Integer knowledgeType;

    @ApiModelProperty("知识库类型名称")
    @TableField("knowledge_type_name")
    private String knowledgeTypeName;

    @ApiModelProperty("知识库附件地址")
    @TableField("attachment_url")
    private String attachmentUrl;

    @ApiModelProperty("知识库网址链接")
    @TableField("website_url")
    private String websiteUrl;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}