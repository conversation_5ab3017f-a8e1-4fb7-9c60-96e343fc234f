package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmPatrolTask对象", description = "巡检任务信息表")
@TableName(value = "usm_patrol_task", autoResultMap = true)
public class UsmPatrolTask extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("任务名称")
    @TableField("task_name")
    private String taskName;

    @ApiModelProperty("巡检对象")
    @TableField("patrol_target")
    private Integer patrolTarget;

    @ApiModelProperty("巡检对象名称")
    @TableField("patrol_target_name")
    private String patrolTargetName;

    @ApiModelProperty("巡检对象id")
    @TableField("patrol_object_id")
    private String patrolObjectId;

    @ApiModelProperty("巡检对象名称")
    @TableField("patrol_object_name")
    private String patrolObjectName;

    @ApiModelProperty("检查单元")
    @TableField("patrol_unit")
    private String patrolUnit;

    @ApiModelProperty("检测方法")
    @TableField("patrol_method")
    private String patrolMethod;

    @ApiModelProperty("检查内容")
    @TableField("patrol_content")
    private String patrolContent;

    @ApiModelProperty("检查结果")
    @TableField("patrol_result")
    private String patrolResult;

    @ApiModelProperty("存在隐患")
    @TableField("exist_danger")
    private String existDanger;

    @ApiModelProperty("检查日期")
    @TableField("patrol_time")
    private Timestamp patrolTime;

    @ApiModelProperty("检查人员")
    @TableField("patrol_user")
    private String patrolUser;

    @ApiModelProperty("设施权属单位code")
    @TableField("facilities_unit")
    private String facilitiesUnit;

    @ApiModelProperty("设施权属单位名称")
    @TableField("facilities_unit_name")
    private String facilitiesUnitName;

    @ApiModelProperty("主管单位code")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("主管单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}