package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmEmergencyExpert对象", description = "应急专家信息表")
@TableName(value = "usm_emergency_expert", autoResultMap = true)
public class UsmEmergencyExpert extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("专家名称")
    @TableField("expert_name")
    private String expertName;

    @ApiModelProperty("专家性别")
    @TableField("expert_gender")
    private String expertGender;

    @ApiModelProperty("专家年龄")
    @TableField("expert_age")
    private String expertAge;

    @ApiModelProperty("工作单位code")
    @TableField("belong_unit")
    private String belongUnit;

    @ApiModelProperty("工作单位名称")
    @TableField("belong_unit_name")
    private String belongUnitName;

    @ApiModelProperty("专业领域")
    @TableField("professional_field")
    private String professionalField;

    @ApiModelProperty("联系电话")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}