package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicWell对象", description = "排水窨井信息表")
@TableName(value = "usm_basic_well", autoResultMap = true)
public class UsmBasicWell extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("窨井编码")
    @TableField("well_code")
    private String wellCode;

    @ApiModelProperty("窨井类型（3001101：雨水井，3001102：污水井，3001103：合流窨井）")
    @TableField("well_type")
    private Integer wellType;

    @ApiModelProperty("窨井类型名称")
    @TableField("well_type_name")
    private String wellTypeName;

    @ApiModelProperty("井深（m）")
    @TableField("well_depth")
    private BigDecimal wellDepth;

    @ApiModelProperty("所在道路")
    @TableField("road_name")
    private String roadName;

    @ApiModelProperty("井盖形状（3001201：圆形，3001202：方形，3001203：其他）")
    @TableField("well_shape")
    private Integer wellShape;

    @ApiModelProperty("井盖形状名称")
    @TableField("well_shape_name")
    private String wellShapeName;

    @ApiModelProperty("井盖材质（3001301：铸铁，3001302：复合材料，3001303：钢，3001304：不锈钢，3001305：聚乙烯，3001306：铝合金，3001307：混凝土，3001308：其他）")
    @TableField("well_material")
    private Integer wellMaterial;

    @ApiModelProperty("井盖材质名称")
    @TableField("well_material_name")
    private String wellMaterialName;

    @ApiModelProperty("井盖尺寸")
    @TableField("well_size")
    private String wellSize;

    @ApiModelProperty("井室规格")
    @TableField("well_room_standard")
    private String wellRoomStandard;

    @ApiModelProperty("特征点")
    @TableField("feature")
    private String feature;

    @ApiModelProperty("附属物")
    @TableField("attached_facilities")
    private String attachedFacilities;

    @ApiModelProperty("埋深（m）")
    @TableField("buried_depth")
    private BigDecimal buriedDepth;

    @ApiModelProperty("地面高程")
    @TableField("elevation")
    private BigDecimal elevation;

    @ApiModelProperty("关联管线id")
    @TableField("pipeline_id")
    private Long pipelineId;

    @ApiModelProperty("关联管线编码")
    @TableField("pipeline_code")
    private String pipelineCode;

    @ApiModelProperty("建设时间")
    @TableField("construction_time")
    private Timestamp constructionTime;

    @ApiModelProperty("权属单位code")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("窨井几何形状")
    @TableField("geom")
    private Object geom;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}