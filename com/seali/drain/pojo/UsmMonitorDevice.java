package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorDevice对象", description = "排水设备信息")
@TableName(value = "usm_monitor_device", autoResultMap = true)
public class UsmMonitorDevice extends BasePojo {

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("设备唯一标识")
    @TableField("data_id")
    private String dataId;

    @ApiModelProperty("设备编码")
    @TableField("index_code")
    private String indexCode;

    @ApiModelProperty("设备名称")
    @TableField("device_name")
    private String deviceName;

    @ApiModelProperty("设备类型编码（3002001：污水溢流，3002002：管网流量，3002003：雨量，3002004：易涝点积水，3002005：水质，3002006：井盖，3002007：可燃气体）")
    @TableField("device_type")
    private String deviceType;

    @ApiModelProperty("设备类型名称")
    @TableField("device_type_name")
    private String deviceTypeName;

    @ApiModelProperty("监测类型")
    @TableField("monitor_type")
    private String monitorType;

    @ApiModelProperty("监测类型名称")
    @TableField("monitor_type_name")
    private String monitorTypeName;

    @ApiModelProperty("监测指标编码（3002101：污水溢流监测（液位：m），3002102：管网流量监测（流量：m3/h），3002103：雨量监测（雨量：mm），3002104：易涝点积水监测（水位：m），3002105：排污水质监测（PH，氨氮：mg/L，CODcr：mg/L，总磷：mg/L），3002106：井盖状态监测（位移：mm，沉降：mm，倾斜：°，振动：HZ），3002107：可燃气体监测（CH₄：%LEL，H₂S：mg/m³，CO：mg/m³））")
    @TableField("monitor_index")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    @TableField("monitor_index_name")
    private String monitorIndexName;

    @ApiModelProperty("监测对象编码（3002201：管线，3002202：水厂，3002203：泵站，3002204：窨井）")
    @TableField("monitor_target")
    private String monitorTarget;

    @ApiModelProperty("监测对象名称")
    @TableField("monitor_target_name")
    private String monitorTargetName;

    @ApiModelProperty("监测对象id")
    @TableField("monitor_object_id")
    private String monitorObjectId;

    @ApiModelProperty("监测对象名称")
    @TableField("monitor_object_name")
    private String monitorObjectName;

    @ApiModelProperty("采集频率(次/分钟)")
    @TableField("collect_frequency")
    private BigDecimal collectFrequency;

    @ApiModelProperty("上传频率(次/分钟)")
    @TableField("upload_frequency")
    private BigDecimal uploadFrequency;

    @ApiModelProperty("量程下线")
    @TableField("measure_range_low")
    private String measureRangeLow;

    @ApiModelProperty("量程上线")
    @TableField("measure_range_up")
    private String measureRangeUp;

    @ApiModelProperty("量程单位")
    @TableField("measure_unit")
    private String measureUnit;

    @ApiModelProperty("区域编码")
    @TableField("region_code")
    private String regionCode;

    @ApiModelProperty("区域名称")
    @TableField("region_name")
    private String regionName;

    @ApiModelProperty("区域Path")
    @TableField("region_path")
    private String regionPath;

    @ApiModelProperty("区域全称")
    @TableField("region_path_name")
    private String regionPathName;

    @ApiModelProperty("安装地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("空间坐标，及经纬度")
    @TableField("geom")
    private Object geom;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private String longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private String latitude;

    @ApiModelProperty("在线状态（3002301：使用中，3002302：已停用）")
    @TableField("online_status")
    private Integer onlineStatus;

    @ApiModelProperty("有视频能力")
    @TableField("is_vss")
    private String isVss;

    @ApiModelProperty("图片URL列表")
    @TableField("pic_urls")
    private String picUrls;

    @ApiModelProperty("数据更新时间")
    @TableField("time")
    private Timestamp time;

    @ApiModelProperty("权属单位Code")
    @TableField("ownership_unit")
    private String ownershipUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("ownership_unit_name")
    private String ownershipUnitName;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

}