package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmAlarmThreshold对象", description = "报警阈值配置表")
@TableName(value = "usm_alarm_threshold", autoResultMap = true)
public class UsmAlarmThreshold extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("规则名称")
    @TableField("rule_name")
    private String ruleName;

    @ApiModelProperty("规则描述")
    @TableField("rule_desc")
    private String ruleDesc;

    @ApiModelProperty("是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @ApiModelProperty("设备类型")
    @TableField("device_type")
    private String deviceType;

    @ApiModelProperty("监测指标")
    @TableField("monitor_index")
    private String monitorIndex;

    @ApiModelProperty("一级报警阈值下限")
    @TableField("threshold_level1_min")
    private BigDecimal thresholdLevel1Min;

    @ApiModelProperty("一级报警阈值上限")
    @TableField("threshold_level1_max")
    private BigDecimal thresholdLevel1Max;

    @ApiModelProperty("一级报警权属单位通知")
    @TableField("notify_rights_dept1")
    private Boolean notifyRightsDept1;

    @ApiModelProperty("一级报警监管部门通知")
    @TableField("notify_supervise_dept1")
    private Boolean notifySuperviseDept1;

    @ApiModelProperty("一级报警通知的监管部门ID列表")
    @TableField("notify_supervise_dept_ids1")
    private String notifySuperviseDeptIds1;

    @ApiModelProperty("二级报警阈值下限")
    @TableField("threshold_level2_min")
    private BigDecimal thresholdLevel2Min;

    @ApiModelProperty("二级报警阈值上限")
    @TableField("threshold_level2_max")
    private BigDecimal thresholdLevel2Max;

    @ApiModelProperty("二级报警权属单位通知")
    @TableField("notify_rights_dept2")
    private Boolean notifyRightsDept2;

    @ApiModelProperty("二级报警监管部门通知")
    @TableField("notify_supervise_dept2")
    private Boolean notifySuperviseDept2;

    @ApiModelProperty("二级报警通知的监管部门ID列表")
    @TableField("notify_supervise_dept_ids2")
    private String notifySuperviseDeptIds2;

    @ApiModelProperty("三级报警阈值下限")
    @TableField("threshold_level3_min")
    private BigDecimal thresholdLevel3Min;

    @ApiModelProperty("三级报警阈值上限")
    @TableField("threshold_level3_max")
    private BigDecimal thresholdLevel3Max;

    @ApiModelProperty("三级报警权属单位通知")
    @TableField("notify_rights_dept3")
    private Boolean notifyRightsDept3;

    @ApiModelProperty("三级报警监管部门通知")
    @TableField("notify_supervise_dept3")
    private Boolean notifySuperviseDept3;

    @ApiModelProperty("三级报警通知的监管部门ID列表")
    @TableField("notify_supervise_dept_ids3")
    private String notifySuperviseDeptIds3;

    @ApiModelProperty("系统通知")
    @TableField("notify_system")
    private Boolean notifySystem;

    @ApiModelProperty("邮件通知")
    @TableField("notify_email")
    private Boolean notifyEmail;

    @ApiModelProperty("短信通知")
    @TableField("notify_sms")
    private Boolean notifySms;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}