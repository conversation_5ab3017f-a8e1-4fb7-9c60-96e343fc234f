package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmCaseLibrary对象", description = "案例库信息表")
@TableName(value = "usm_case_library", autoResultMap = true)
public class UsmCaseLibrary extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("案例名称")
    @TableField("case_name")
    private String caseName;

    @ApiModelProperty("事故概述")
    @TableField("case_overview")
    private String caseOverview;

    @ApiModelProperty("事故发生日期")
    @TableField("case_time")
    private Timestamp caseTime;

    @ApiModelProperty("经济损失情况")
    @TableField("economy_loss")
    private String economyLoss;

    @ApiModelProperty("事故伤亡情况")
    @TableField("casualties")
    private String casualties;

    @ApiModelProperty("事故原因描述")
    @TableField("reason")
    private String reason;

    @ApiModelProperty("处置之前图片")
    @TableField("before_pic_urls")
    private String beforePicUrls;

    @ApiModelProperty("发布单位")
    @TableField("publish_unit")
    private String publishUnit;

    @ApiModelProperty("发布时间")
    @TableField("publish_time")
    private Timestamp publishTime;

    @ApiModelProperty("调查报告")
    @TableField("research_report_url")
    private String researchReportUrl;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}