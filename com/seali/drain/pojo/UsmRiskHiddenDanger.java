package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmRiskHiddenDanger对象", description = "排水隐患信息表")
@TableName(value = "usm_risk_hidden_danger", autoResultMap = true)
public class UsmRiskHiddenDanger extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("隐患来源（3002601：人工上报，3002602：系统同步）")
    @TableField("danger_source_type")
    private String dangerSourceType;

    @ApiModelProperty("隐患来源名称")
    @TableField("danger_source_name")
    private String dangerSourceName;

    @ApiModelProperty("隐患描述")
    @TableField("danger_desc")
    private String dangerDesc;

    @ApiModelProperty("隐患类型（3002801：管道破裂隐患，3002802：管道堵塞隐患，3002803：水质污染隐患，3002804：排水不足/泄洪隐患，3002805：管道腐蚀隐患，3002806：地质灾害隐患，3002807：其他）")
    @TableField("danger_type")
    private Integer dangerType;

    @ApiModelProperty("隐患类型名称")
    @TableField("danger_type_name")
    private String dangerTypeName;

    @ApiModelProperty("隐患等级（3002701：重大隐患，3002702：较大隐患，3002703：一般隐患）")
    @TableField("danger_level")
    private Integer dangerLevel;

    @ApiModelProperty("隐患等级名称")
    @TableField("danger_level_name")
    private String dangerLevelName;

    @ApiModelProperty("隐患对象编码（3002901：管线，3002902：排水口，3002903：污水厂，3002904：泵站，3002905：窨井，3002906：易涝点，3002907：设备）")
    @TableField("danger_target")
    private Integer dangerTarget;

    @ApiModelProperty("隐患对象名称")
    @TableField("danger_target_name")
    private String dangerTargetName;

    @ApiModelProperty("隐患对象id")
    @TableField("danger_object_id")
    private String dangerObjectId;

    @ApiModelProperty("隐患对象名称")
    @TableField("danger_object_name")
    private String dangerObjectName;

    @ApiModelProperty("整改期限")
    @TableField("rectification_deadline")
    private Timestamp rectificationDeadline;

    @ApiModelProperty("上报人")
    @TableField("report_user")
    private String reportUser;

    @ApiModelProperty("上报时间")
    @TableField("report_time")
    private Timestamp reportTime;

    @ApiModelProperty("权属单位id")
    @TableField("ownership_unit")
    private String ownershipUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("ownership_unit_name")
    private String ownershipUnitName;

    @ApiModelProperty("责任人id")
    @TableField("responsible_user_id")
    private String responsibleUserId;

    @ApiModelProperty("责任人名称")
    @TableField("responsible_user_name")
    private String responsibleUserName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("隐患图片")
    @TableField("pic_urls")
    private String picUrls;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

    @ApiModelProperty("隐患状态（3003001:待整改，3003002:整改中，3003003:待复查，3003004:已整改）")
    @TableField("danger_status")
    private Integer dangerStatus;

    @ApiModelProperty("隐患状态名称")
    @TableField("danger_status_name")
    private String dangerStatusName;

}