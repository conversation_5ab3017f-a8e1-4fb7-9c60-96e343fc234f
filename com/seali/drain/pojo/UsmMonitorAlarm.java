package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorAlarm对象", description = "设备报警表")
@TableName(value = "usm_monitor_alarm", autoResultMap = true)
public class UsmMonitorAlarm extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("报警设备ID")
    @TableField("device_id")
    private String deviceId;

    @ApiModelProperty("报警来源")
    @TableField("alarm_source")
    private String alarmSource;

    @ApiModelProperty("报警编号")
    @TableField("alarm_code")
    private String alarmCode;

    @ApiModelProperty("报警时间")
    @TableField("alarm_time")
    private Timestamp alarmTime;

    @ApiModelProperty("监测对象ID")
    @TableField("monitor_object_id")
    private String monitorObjectId;

    @ApiModelProperty("监测对象名称")
    @TableField("monitor_object_name")
    private String monitorObjectName;

    @ApiModelProperty("报警值")
    @TableField("alarm_value")
    private String alarmValue;

    @ApiModelProperty("报警位置")
    @TableField("alarm_location")
    private String alarmLocation;

    @ApiModelProperty("报警级别（3003601:一级, 3003602:二级, 3003603:三级, 3003604:四级）")
    @TableField("alarm_level")
    private Integer alarmLevel;

    @ApiModelProperty("报警级别名称")
    @TableField("alarm_level_name")
    private String alarmLevelName;

    @ApiModelProperty("报警状态（3003701:待确认,3003702:误报,3003703:待处置, 3003704:处置中, 3003705:已处置,3003706:已归档）")
    @TableField("alarm_status")
    private Integer alarmStatus;

    @ApiModelProperty("报警状态名称")
    @TableField("alarm_status_name")
    private String alarmStatusName;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

    @ApiModelProperty("设备监测数据Id：如usm_fixed_point_laser_methane_monitor的id")
    @TableField("monitor_data_id")
    private String monitorDataId;

    @ApiModelProperty("报警类型（3003101：污水溢流监测报警，3003102：管网流量监测报警，3003103：雨量监测报警，3003104：易涝点积水监测报警，3003105：排污水质监测报警，3003106：井盖状态监测报警，3003107：可燃气体监测报警）")
    @TableField("alarm_type")
    private Integer alarmType;

    @ApiModelProperty("报警类型名称")
    @TableField("alarm_type_name")
    private String alarmTypeName;

}