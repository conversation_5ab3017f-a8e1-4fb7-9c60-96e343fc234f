package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorIndicators对象", description = "监测指标表")
@TableName(value = "usm_monitor_indicators", autoResultMap = true)
public class UsmMonitorIndicators extends BasePojo {

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("设备器类型，对应usm_monitor_device.device_type")
    @TableField("device_type")
    private String deviceType;

    @ApiModelProperty("监测指标编码，多个监测指标创建多条，5002001-水位,5002002-温度,5002003-液位,5002004-浓度,5002005-井盖状态,5002006-流量,5002007-流速,5002008-角度,5002009-水浸状态,5002010-化学需氧量（COD）,5002011-浊度")
    @TableField("monitor_index")
    private String monitorIndex;

    @ApiModelProperty("监测指标名称")
    @TableField("monitor_index_name")
    private String monitorIndexName;

    @ApiModelProperty("量程下线")
    @TableField("measure_range_low")
    private String measureRangeLow;

    @ApiModelProperty("量程上线")
    @TableField("measure_range_up")
    private String measureRangeUp;

    @ApiModelProperty("量程单位")
    @TableField("measure_unit")
    private String measureUnit;

    @ApiModelProperty("监测字段，如检测的是浓度，填ld，对应usm_monitor_record.ld字段")
    @TableField("monitor_field")
    private String monitorField;

    @ApiModelProperty("类型：0-监测指标是状态，1-监测指标是数值")
    @TableField("type")
    private Integer type;

}