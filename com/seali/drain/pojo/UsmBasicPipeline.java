package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicPipeline对象", description = "管线信息表")
@TableName(value = "usm_basic_pipeline", autoResultMap = true)
public class UsmBasicPipeline extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("管线编码")
    @TableField("pipeline_code")
    private String pipelineCode;

    @ApiModelProperty("管线类型（3000101：污水管线，3000102：雨水管线，3000103：雨污合流管线）")
    @TableField("pipeline_type")
    private Integer pipelineType;

    @ApiModelProperty("管线类型名称")
    @TableField("pipeline_type_name")
    private String pipelineTypeName;

    @ApiModelProperty("压力类型")
    @TableField("pressure_type")
    private Integer pressureType;

    @ApiModelProperty("压力类型名称")
    @TableField("pressure_type_name")
    private String pressureTypeName;

    @ApiModelProperty("设计压力（Mpa）")
    @TableField("design_pressure")
    private BigDecimal designPressure;

    @ApiModelProperty("埋设方式（3000201：地埋式铺设，3000202：架空铺设，3000203：水平定向钻铺设，3000204：管道隧道铺设）")
    @TableField("buried_type")
    private Integer buriedType;

    @ApiModelProperty("埋设方式名称")
    @TableField("buried_type_name")
    private String buriedTypeName;

    @ApiModelProperty("材质（3000301：PE管，3000302：PVC管，3000303：RCP管，3000304：FRP管，3000305：球墨铸铁管，3000306：不锈钢管，3000307：钢塑复合管，3000308：其他）")
    @TableField("material")
    private Integer material;

    @ApiModelProperty("材质名称")
    @TableField("material_name")
    private String materialName;

    @ApiModelProperty("管径（DN）")
    @TableField("pipe_diameter")
    private String pipeDiameter;

    @ApiModelProperty("长度（m）")
    @TableField("pipe_length")
    private BigDecimal pipeLength;

    @ApiModelProperty("流向")
    @TableField("flow_direction")
    private String flowDirection;

    @ApiModelProperty("所在道路")
    @TableField("road_name")
    private String roadName;

    @ApiModelProperty("起点埋深（m）")
    @TableField("start_point_depth")
    private BigDecimal startPointDepth;

    @ApiModelProperty("起点高程（m）")
    @TableField("start_point_distance")
    private BigDecimal startPointDistance;

    @ApiModelProperty("起点经度")
    @TableField("start_point_longitude")
    private BigDecimal startPointLongitude;

    @ApiModelProperty("起点纬度")
    @TableField("start_point_latitude")
    private BigDecimal startPointLatitude;

    @ApiModelProperty("终点埋深（m）")
    @TableField("end_point_depth")
    private BigDecimal endPointDepth;

    @ApiModelProperty("终点高程（m）")
    @TableField("end_point_distance")
    private BigDecimal endPointDistance;

    @ApiModelProperty("终点经度")
    @TableField("end_point_longitude")
    private BigDecimal endPointLongitude;

    @ApiModelProperty("终点纬度")
    @TableField("end_point_latitude")
    private BigDecimal endPointLatitude;

    @ApiModelProperty("断面类型（3000401：圆形，3000402：梯形，3000403：三角形，3000404：椭圆形，3000405：矩形，3000406：马蹄形，3000407：不规则形状）")
    @TableField("section_type")
    private Integer sectionType;

    @ApiModelProperty("断面类型名称")
    @TableField("section_type_name")
    private String sectionTypeName;

    @ApiModelProperty("建设时间")
    @TableField("construction_time")
    private Timestamp constructionTime;

    @ApiModelProperty("权属单位")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("使用状态（3000501：使用中，3000502：报废，3000503：未使用）")
    @TableField("usage_status")
    private Integer usageStatus;

    @ApiModelProperty("使用状态名称")
    @TableField("usage_status_name")
    private String usageStatusName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("管线几何形状")
    @TableField("geom")
    private Object geom;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}