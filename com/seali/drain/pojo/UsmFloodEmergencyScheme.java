package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmFloodEmergencyScheme对象", description = "防汛应急辅助决策方案信息表")
@TableName(value = "usm_flood_emergency_scheme", autoResultMap = true)
public class UsmFloodEmergencyScheme extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("方案名称")
    @TableField("scheme_name")
    private String schemeName;

    @ApiModelProperty("来源单位编码")
    @TableField("source_unit")
    private String sourceUnit;

    @ApiModelProperty("来源单位名称")
    @TableField("source_unit_name")
    private String sourceUnitName;

    @ApiModelProperty("匹配报警类型编码")
    @TableField("alarm_type")
    private Integer alarmType;

    @ApiModelProperty("匹配报警类型名称")
    @TableField("alarm_type_name")
    private String alarmTypeName;

    @ApiModelProperty("匹配事件类型")
    @TableField("event_type")
    private Integer eventType;

    @ApiModelProperty("匹配事件类型名称")
    @TableField("event_type_name")
    private String eventTypeName;

    @ApiModelProperty("编制日期")
    @TableField("edit_date")
    private Timestamp editDate;

    @ApiModelProperty("附件地址")
    @TableField("file_url")
    private String fileUrl;

    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}