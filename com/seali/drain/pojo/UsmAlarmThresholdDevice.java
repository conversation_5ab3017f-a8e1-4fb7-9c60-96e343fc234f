package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmAlarmThresholdDevice对象", description = "报警阈值-设备关联表")
@TableName(value = "usm_alarm_threshold_device", autoResultMap = true)
public class UsmAlarmThresholdDevice extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("报警阈值ID")
    @TableField("threshold_id")
    private String thresholdId;

    @ApiModelProperty("设备ID")
    @TableField("device_id")
    private String deviceId;

}