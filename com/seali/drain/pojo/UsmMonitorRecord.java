package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmMonitorRecord对象", description = "设备监测记录表")
@TableName(value = "usm_monitor_record", autoResultMap = true)
public class UsmMonitorRecord extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("设备ID")
    @TableField("device_id")
    private String deviceId;

    @ApiModelProperty("监测时间")
    @TableField("monitor_time")
    private Timestamp monitorTime;

    @ApiModelProperty("中台id")
    @TableField("data_id")
    private String dataId;

    @ApiModelProperty("流量(m³/h)")
    @TableField("mon_ll")
    private Object monLl;

    @ApiModelProperty("流速(m/s)")
    @TableField("mon_ls")
    private Object monLs;

    @ApiModelProperty("水位(m)")
    @TableField("mon_sw")
    private Object monSw;

    @ApiModelProperty("化学需氧量COD(mg/L)")
    @TableField("mon_cod")
    private Object monCod;

    @ApiModelProperty("浓度(LEL%)")
    @TableField("mon_nd")
    private Object monNd;

    @ApiModelProperty("浊度(NTU)")
    @TableField("mon_zd")
    private Object monZd;

    @ApiModelProperty("温度(℃)")
    @TableField("mon_wd")
    private Object monWd;

    @ApiModelProperty("角度(°)")
    @TableField("mon_jd")
    private Object monJd;

    @ApiModelProperty("压力(MPa)")
    @TableField("mon_yl")
    private Object monYl;

    @ApiModelProperty("井盖状态(0正常;1打开)")
    @TableField("mon_jgzt")
    private Integer monJgzt;

    @ApiModelProperty("水浸状态(0正常;1水浸)")
    @TableField("mon_sjzt")
    private Integer monSjzt;

}