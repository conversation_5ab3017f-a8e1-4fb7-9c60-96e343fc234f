package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmRiskAssessmentPipeline对象", description = "管网风险评估表")
@TableName(value = "usm_risk_assessment_pipeline", autoResultMap = true)
public class UsmRiskAssessmentPipeline extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("风险编码")
    @TableField("risk_code")
    private String riskCode;

    @ApiModelProperty("管线ID")
    @TableField("pipeline_id")
    private String pipelineId;

    @ApiModelProperty("评估记录(0:系统评估, 1:风险修改)")
    @TableField("assessment_type")
    private Integer assessmentType;

    @ApiModelProperty("风险等级（3002401：重大风险，3002402：较大风险，3002403：一般风险，3002404：低风险）")
    @TableField("risk_level")
    private Integer riskLevel;

    @ApiModelProperty("风险等级名称")
    @TableField("risk_level_name")
    private String riskLevelName;

    @ApiModelProperty("管控状态（3002501：无需管控，3002502：已管控，3002503：未管控）")
    @TableField("pipeline_status")
    private Integer pipelineStatus;

    @ApiModelProperty("管控状态名称")
    @TableField("pipeline_status_name")
    private String pipelineStatusName;

    @ApiModelProperty("风险描述")
    @TableField("risk_description")
    private String riskDescription;

    @ApiModelProperty("风险管控措施描述")
    @TableField("risk_control_measures")
    private String riskControlMeasures;

    @ApiModelProperty("评估日期")
    @TableField("assessment_date")
    private Date assessmentDate;

    @ApiModelProperty("评估人")
    @TableField("assessor")
    private String assessor;

    @ApiModelProperty("评估风险值")
    @TableField("assess_risk_value")
    private String assessRiskValue;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

}