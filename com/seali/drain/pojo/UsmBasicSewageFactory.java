package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicSewageFactory对象", description = "污水厂信息表")
@TableName(value = "usm_basic_sewage_factory", autoResultMap = true)
public class UsmBasicSewageFactory extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("污水厂名称")
    @TableField("factory_name")
    private String factoryName;

    @ApiModelProperty("污水厂类型（3001501：市政污水厂，3001502：工业废水厂，3001503：农村分散式污水厂，3001504：再生水厂）")
    @TableField("factory_type")
    private Integer factoryType;

    @ApiModelProperty("污水厂类型名称")
    @TableField("factory_type_name")
    private String factoryTypeName;

    @ApiModelProperty("污水处理级别")
    @TableField("handle_level")
    private String handleLevel;

    @ApiModelProperty("污水处理设计规模（m³/天）")
    @TableField("handle_model")
    private String handleModel;

    @ApiModelProperty("所在道路")
    @TableField("road_name")
    private String roadName;

    @ApiModelProperty("占地面积")
    @TableField("floor_area")
    private String floorArea;

    @ApiModelProperty("污水处理设施类型")
    @TableField("facility_type")
    private String facilityType;

    @ApiModelProperty("排水去向")
    @TableField("drain_direction")
    private String drainDirection;

    @ApiModelProperty("处理工艺")
    @TableField("handle_technology")
    private String handleTechnology;

    @ApiModelProperty("污泥去向")
    @TableField("sludge_direction")
    private String sludgeDirection;

    @ApiModelProperty("运行时间")
    @TableField("construction_time")
    private Timestamp constructionTime;

    @ApiModelProperty("建设单位code")
    @TableField("construction_unit")
    private String constructionUnit;

    @ApiModelProperty("建设单位名称")
    @TableField("construction_unit_name")
    private String constructionUnitName;

    @ApiModelProperty("联系人")
    @TableField("contact_user")
    private String contactUser;

    @ApiModelProperty("联系电话")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty("使用状态（3000501：使用中，3000502：报废，3000503：未使用）")
    @TableField("usage_status")
    private Integer usageStatus;

    @ApiModelProperty("使用状态名称")
    @TableField("usage_status_name")
    private String usageStatusName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}