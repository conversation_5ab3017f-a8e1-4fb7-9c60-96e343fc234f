package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicDrainGrate对象", description = "雨水篦子信息表")
@TableName(value = "usm_basic_drain_grate", autoResultMap = true)
public class UsmBasicDrainGrate extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("雨水篦子编码")
    @TableField("grate_code")
    private String grateCode;

    @ApiModelProperty("雨水篦子名称")
    @TableField("grate_name")
    private String grateName;

    @ApiModelProperty("材质（3000901：铸铁，3000902：钢制，3000903：复合材料，3000904：混凝土，3000905：其他）")
    @TableField("material_type")
    private Integer materialType;

    @ApiModelProperty("材质名称")
    @TableField("material_type_name")
    private String materialTypeName;

    @ApiModelProperty("形状（3001001：矩形，3001002：圆形异形）")
    @TableField("shape_type")
    private Integer shapeType;

    @ApiModelProperty("形状名称")
    @TableField("shape_type_name")
    private String shapeTypeName;

    @ApiModelProperty("尺寸（mm）")
    @TableField("grate_size")
    private String grateSize;

    @ApiModelProperty("权属单位code")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}