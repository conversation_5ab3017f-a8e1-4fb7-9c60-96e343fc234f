package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmEmergencyTeam对象", description = "应急队伍信息表")
@TableName(value = "usm_emergency_team", autoResultMap = true)
public class UsmEmergencyTeam extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("应急队伍名称")
    @TableField("team_name")
    private String teamName;

    @ApiModelProperty("应急队伍类型")
    @TableField("team_type")
    private Integer teamType;

    @ApiModelProperty("应急队伍类型名称")
    @TableField("team_type_name")
    private String teamTypeName;

    @ApiModelProperty("应急队伍级别")
    @TableField("team_level")
    private Integer teamLevel;

    @ApiModelProperty("应急队伍级别名称")
    @TableField("team_level_name")
    private String teamLevelName;

    @ApiModelProperty("人数")
    @TableField("team_people_num")
    private Integer teamPeopleNum;

    @ApiModelProperty("成立时间")
    @TableField("establish_time")
    private Timestamp establishTime;

    @ApiModelProperty("管理单位code")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("管理单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("负责人")
    @TableField("responsible_user")
    private String responsibleUser;

    @ApiModelProperty("负责人电话")
    @TableField("responsible_user_phone")
    private String responsibleUserPhone;

    @ApiModelProperty("联系人1")
    @TableField("contact_user_1")
    private String contactUser1;

    @ApiModelProperty("联系人1电话")
    @TableField("contact_info_1")
    private String contactInfo1;

    @ApiModelProperty("联系人2")
    @TableField("contact_user_2")
    private String contactUser2;

    @ApiModelProperty("联系人2电话")
    @TableField("contact_info_2")
    private String contactInfo2;

    @ApiModelProperty("座机")
    @TableField("landline")
    private String landline;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("社区编码")
    @TableField("community")
    private String community;

    @ApiModelProperty("社区名称")
    @TableField("community_name")
    private String communityName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}