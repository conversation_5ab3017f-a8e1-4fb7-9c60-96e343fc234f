package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicFloodChannel对象", description = "行泄通道信息表")
@TableName(value = "usm_basic_flood_channel", autoResultMap = true)
public class UsmBasicFloodChannel extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("地表雨水行泄通道类型")
    @TableField("channel_type")
    private String channelType;

    @ApiModelProperty("地表雨水行泄通道长度")
    @TableField("channel_length")
    private BigDecimal channelLength;

    @ApiModelProperty("地表雨水行泄通道面积")
    @TableField("channel_area")
    private String channelArea;

    @ApiModelProperty("地表雨水行泄通道糙率信息")
    @TableField("channel_roughness")
    private String channelRoughness;

    @ApiModelProperty("所在道路")
    @TableField("road_name")
    private String roadName;

    @ApiModelProperty("路面等级")
    @TableField("road_level")
    private String roadLevel;

    @ApiModelProperty("面层类型")
    @TableField("road_type")
    private String roadType;

    @ApiModelProperty("权属单位code")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}