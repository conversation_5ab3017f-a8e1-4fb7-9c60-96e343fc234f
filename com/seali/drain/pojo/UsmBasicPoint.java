package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicPoint对象", description = "管点信息表")
@TableName(value = "usm_basic_point", autoResultMap = true)
public class UsmBasicPoint extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("管点编码")
    @TableField("point_code")
    private String pointCode;

    @ApiModelProperty("管点类型（3000601：阀门，3000602：弯头，3000603：变径点，3000604：变材点，3000605：多通点，3000606：检测点，3000607：探测点，3000608：预留口，3000609：非普查区，3000610：变深点，3000611：其他）")
    @TableField("point_type")
    private Integer pointType;

    @ApiModelProperty("管点类型名称")
    @TableField("point_type_name")
    private String pointTypeName;

    @ApiModelProperty("埋深(米)")
    @TableField("depth")
    private BigDecimal depth;

    @ApiModelProperty("高程(米)")
    @TableField("elevation")
    private BigDecimal elevation;

    @ApiModelProperty("附属设施描述")
    @TableField("attached_facilities")
    private String attachedFacilities;

    @ApiModelProperty("所在道路名称")
    @TableField("road_name")
    private String roadName;

    @ApiModelProperty("关联管线id")
    @TableField("connected_pipeline_id")
    private String connectedPipelineId;

    @ApiModelProperty("关联管线信息")
    @TableField("connected_pipeline")
    private String connectedPipeline;

    @ApiModelProperty("关联管井id")
    @TableField("connected_well_id")
    private String connectedWellId;

    @ApiModelProperty("关联管井信息")
    @TableField("connected_well")
    private String connectedWell;

    @ApiModelProperty("权属单位编码")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("管点几何形状")
    @TableField("geom")
    private Object geom;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("安装时间")
    @TableField("install_time")
    private Date installTime;

    @ApiModelProperty("使用状态（3000501：使用中，3000502：报废，3000503：未使用）")
    @TableField("usage_status")
    private Integer usageStatus;

    @ApiModelProperty("使用状态名称")
    @TableField("usage_status_name")
    private String usageStatusName;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}