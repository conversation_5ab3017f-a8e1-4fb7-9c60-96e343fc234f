package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicRepair对象", description = "管线维修记录表")
@TableName(value = "usm_basic_repair", autoResultMap = true)
public class UsmBasicRepair extends BasePojo {

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("维修单号")
    @TableField("repair_code")
    private String repairCode;

    @ApiModelProperty("关联管线id")
    @TableField("connected_pipeline_id")
    private String connectedPipelineId;

    @ApiModelProperty("关联管线编码")
    @TableField("connected_pipeline")
    private String connectedPipeline;

    @ApiModelProperty("维修类型")
    @TableField("repair_type")
    private Integer repairType;

    @ApiModelProperty("维修类型名称")
    @TableField("repair_type_name")
    private String repairTypeName;

    @ApiModelProperty("维修内容")
    @TableField("repair_desc")
    private String repairDesc;

    @ApiModelProperty("附属设施")
    @TableField("attached_facilities")
    private String attachedFacilities;

    @ApiModelProperty("维修结果（3000701：已完成，3000702：未完成）")
    @TableField("repair_result")
    private String repairResult;

    @ApiModelProperty("维修时间")
    @TableField("repair_time")
    private Date repairTime;

    @ApiModelProperty("维修方案")
    @TableField("repair_scheme")
    private String repairScheme;

    @ApiModelProperty("维修人")
    @TableField("repair_user")
    private String repairUser;

    @ApiModelProperty("联系电话")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty("权属单位编码")
    @TableField("management_unit")
    private String managementUnit;

    @ApiModelProperty("权属单位名称")
    @TableField("management_unit_name")
    private String managementUnitName;

    @ApiModelProperty("维修前照片")
    @TableField("before_pic_url")
    private String beforePicUrl;

    @ApiModelProperty("维修后照片")
    @TableField("after_pic_url")
    private String afterPicUrl;

    @ApiModelProperty("附件")
    @TableField("file_url")
    private String fileUrl;

    @ApiModelProperty("所属市")
    @TableField("city")
    private String city;

    @ApiModelProperty("所属区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("所属区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("所属乡镇编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("所属乡镇名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}