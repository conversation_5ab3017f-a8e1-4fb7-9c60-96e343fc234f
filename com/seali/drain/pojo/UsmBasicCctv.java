package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicCctv对象", description = "CCTV检测信息表")
@TableName(value = "usm_basic_cctv", autoResultMap = true)
public class UsmBasicCctv extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("关联管线id")
    @TableField("pipeline_id")
    private String pipelineId;

    @ApiModelProperty("管道结构性缺陷类型（3001601：破裂，3001602：变形，3001603：腐蚀，3001604：错口，3001605：脱节，3001606：塌陷，3001607：支管暗接，3001608：渗漏）")
    @TableField("defect_type")
    private Integer defectType;

    @ApiModelProperty("管道结构性缺陷类型名称")
    @TableField("defect_type_name")
    private String defectTypeName;

    @ApiModelProperty("缺陷等级（3001701：Ⅰ级，3001702：Ⅱ级，3001703：Ⅲ级，3001704：Ⅳ级）")
    @TableField("defect_level")
    private Integer defectLevel;

    @ApiModelProperty("缺陷等级名称")
    @TableField("defect_level_name")
    private String defectLevelName;

    @ApiModelProperty("检测日期")
    @TableField("detect_time")
    private Timestamp detectTime;

    @ApiModelProperty("管道检测现场记录表")
    @TableField("pipe_record_urls")
    private String pipeRecordUrls;

    @ApiModelProperty("检查井检查记录表")
    @TableField("well_record_urls")
    private String wellRecordUrls;

    @ApiModelProperty("管段状况评估表")
    @TableField("pipe_assess_urls")
    private String pipeAssessUrls;

    @ApiModelProperty("排水井检查情况汇总表")
    @TableField("drain_well_summary_urls")
    private String drainWellSummaryUrls;

    @ApiModelProperty("排水管道检测成果表")
    @TableField("drain_pipe_result_urls")
    private String drainPipeResultUrls;

    @ApiModelProperty("分值表")
    @TableField("score_urls")
    private String scoreUrls;

    @ApiModelProperty("排水管道沉积状况纵断面图")
    @TableField("sediment_profile_urls")
    private String sedimentProfileUrls;

    @ApiModelProperty("CCTV检测图片")
    @TableField("cctv_pic_urls")
    private String cctvPicUrls;

    @ApiModelProperty("CCTV检测视频")
    @TableField("cctv_video_urls")
    private String cctvVideoUrls;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}