package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmPatrolPlan对象", description = "巡检计划信息表")
@TableName(value = "usm_patrol_plan", autoResultMap = true)
public class UsmPatrolPlan extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("计划名称")
    @TableField("plan_name")
    private String planName;

    @ApiModelProperty("巡检对象")
    @TableField("patrol_target")
    private Integer patrolTarget;

    @ApiModelProperty("巡检对象名称")
    @TableField("patrol_target_name")
    private String patrolTargetName;

    @ApiModelProperty("巡检对象id")
    @TableField("patrol_object_id")
    private String patrolObjectId;

    @ApiModelProperty("巡检对象名称")
    @TableField("patrol_object_name")
    private String patrolObjectName;

    @ApiModelProperty("检查方法")
    @TableField("patrol_method")
    private String patrolMethod;

    @ApiModelProperty("检查内容")
    @TableField("patrol_content")
    private String patrolContent;

    @ApiModelProperty("巡检周期")
    @TableField("patrol_period")
    private String patrolPeriod;

    @ApiModelProperty("计划开始时间")
    @TableField("start_time")
    private Timestamp startTime;

    @ApiModelProperty("计划结束时间")
    @TableField("end_time")
    private Timestamp endTime;

    @ApiModelProperty("检查人员")
    @TableField("patrol_user")
    private String patrolUser;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

    @ApiModelProperty("检查单元")
    @TableField("patrol_unit")
    private String patrolUnit;

}