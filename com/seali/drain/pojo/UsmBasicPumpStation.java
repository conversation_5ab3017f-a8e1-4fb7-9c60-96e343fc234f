package com.seali.drain.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.seali.common.pojo.BasePojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UsmBasicPumpStation对象", description = "泵站信息表")
@TableName(value = "usm_basic_pump_station", autoResultMap = true)
public class UsmBasicPumpStation extends BasePojo {

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("泵站编码")
    @TableField("station_code")
    private String stationCode;

    @ApiModelProperty("泵站名称")
    @TableField("station_name")
    private String stationName;

    @ApiModelProperty("泵站类型（3001401：污水泵站，3001402：雨水泵站，3001403：合流泵站，3001404：污泥泵站）")
    @TableField("station_type")
    private Integer stationType;

    @ApiModelProperty("泵站类型名称")
    @TableField("station_type_name")
    private String stationTypeName;

    @ApiModelProperty("拥有泵数量")
    @TableField("pump_num")
    private String pumpNum;

    @ApiModelProperty("设计雨水排水能力（m³/s）")
    @TableField("drain_rain_capacity")
    private String drainRainCapacity;

    @ApiModelProperty("设计污水排水能力（m³/s）")
    @TableField("drain_sewage_capacity")
    private String drainSewageCapacity;

    @ApiModelProperty("服务范围")
    @TableField("server_range")
    private String serverRange;

    @ApiModelProperty("服务面积")
    @TableField("server_area")
    private String serverArea;

    @ApiModelProperty("运行时间")
    @TableField("work_time")
    private Timestamp workTime;

    @ApiModelProperty("建设单位code")
    @TableField("construction_unit")
    private String constructionUnit;

    @ApiModelProperty("建设单位名称")
    @TableField("construction_unit_name")
    private String constructionUnitName;

    @ApiModelProperty("联系人")
    @TableField("contact_user")
    private String contactUser;

    @ApiModelProperty("联系电话")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty("使用状态（3000501：使用中，3000502：报废，3000503：未使用）")
    @TableField("usage_status")
    private Integer usageStatus;

    @ApiModelProperty("使用状态名称")
    @TableField("usage_status_name")
    private String usageStatusName;

    @ApiModelProperty("城市")
    @TableField("city")
    private String city;

    @ApiModelProperty("区县编码")
    @TableField("county")
    private String county;

    @ApiModelProperty("区县名称")
    @TableField("county_name")
    private String countyName;

    @ApiModelProperty("乡镇/街道编码")
    @TableField("town")
    private String town;

    @ApiModelProperty("乡镇/街道名称")
    @TableField("town_name")
    private String townName;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Timestamp createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}