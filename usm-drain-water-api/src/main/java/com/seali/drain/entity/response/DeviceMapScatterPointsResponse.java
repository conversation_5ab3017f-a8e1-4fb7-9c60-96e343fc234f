package com.seali.drain.entity.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "DeviceMapScatterPointsResponse", description = "设备地图散点图响应对象")
@Data
public class DeviceMapScatterPointsResponse {

    @ApiModelProperty(value = "设备ID")
    private String id;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "设备空间信息")
    private String geomText;

    @ApiModelProperty(value = "报警状态")
    private String alarmStatus;
}
