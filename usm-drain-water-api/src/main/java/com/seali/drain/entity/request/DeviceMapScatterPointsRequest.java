package com.seali.drain.entity.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "DeviceMapScatterPointsResponse", description = "设备地图散点图请求对象")
@Data
public class DeviceMapScatterPointsRequest {

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "设备空间信息")
    private String polygon;

    @ApiModelProperty(value = "报警状态")
    private String alarmStatus;
}
